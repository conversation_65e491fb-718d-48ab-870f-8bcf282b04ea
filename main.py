# -*- coding: utf-8 -*-
# @Time 2025/5/17 15:15
# @Author: <PERSON>
"""
程序入口
"""
from apscheduler.schedulers.blocking import BlockingScheduler

from config.logConfig import logger
from stockAnalysis.emotionalAspect import emotional_analysis_task
from stockAnalysis.quantityPriceAspect import quantity_price_analysis_task
from stockAnalysis.jbm_js_stock_analysis import jishu_all_stocks
from stockAnalysis.jbm_stock_analysis import filter_and_sort_stocks
from stockAnalysis.ticai import ticai_task
from stockAnalysis.SelectStock_win import select_stocks_win 
from datetime import datetime, timedelta

# 导入时间管理器
from utils.commonUtil import datetime_manager, is_trading_day

if __name__ == "__main__":
    def dingshi_task():
        # 统一设置全局当前时间
        now = datetime.now()
        datetime_manager.set_current_datetime(now)
        date = datetime_manager.get_current_datetime()
        date_str = date.strftime("%Y-%m-%d")
        
        # 判断是否为交易日
        if not is_trading_day(date):
            logger.info(f"当前日期 {date_str} 为非交易日，跳过任务执行")
            return
        
        logger.info(f"当前日期 {date_str} 为交易日，开始执行任务")
        
        ticai_task(date_str)
        quantity_price_analysis_task(date_str)
        emotional_analysis_task(date_str)
        filter_and_sort_stocks(date_str)
        jishu_all_stocks(date_str)
        select_stocks_win(date_str)
    # dingshi_task()



    # 创建任务调度器
    scheduler = BlockingScheduler(timezone='Asia/Shanghai')

    logger.info("设置任务调度器")

    scheduler.add_job(dingshi_task, "cron", hour=18, minute=50)
    
    logger.info("设置定时任务，固定每天下午6点50执行")

    scheduler.start()
    logger.info("调度器启动")

