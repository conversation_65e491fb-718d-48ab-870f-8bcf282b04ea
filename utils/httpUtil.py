# -*- coding: utf-8 -*-
# @Time 2025/5/16 19:42
# @Author: <PERSON>
"""
请求金融数据工具
"""
import traceback

import pandas as pd
import requests
import json

from config.logConfig import logger
from config.constant import URL_GROUPS, TOKEN_GROUPS

# 跌停股池
def get_dtgc_data(start_date, end_date, fields="all"):
    url = URL_GROUPS["WAIZAOWANG_DTGC_URL"]
    token = TOKEN_GROUPS["WAIZAOWANG_API_TOKEN"]
    params = {
        "startDate": start_date,
        "endDate": end_date,
        "fields": fields,
        "export": 1,
        "token": token
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        result = response.json()
        if result.get("code") == 200:
            data = result.get("data", [])
            if isinstance(data, list):
                return data
            elif isinstance(data, dict):
                return [data]
            else:
                logger.warning(f"跌停股池数据格式异常: {type(data)}")
                return []
        else:
            logger.error(f"跌停股池接口返回错误: code={result.get('code')}, msg={result.get('msg')}")
            return []
    except Exception as e:
        logger.error(f"获取跌停股池数据失败: {e}")
        return []

# 涨停股池
def get_ztgc_data(start_date, end_date, fields="all"):
    url = URL_GROUPS["WAIZAOWANG_ZTGC_URL"]
    token = TOKEN_GROUPS["WAIZAOWANG_API_TOKEN"]
    params = {
        "startDate": start_date,
        "endDate": end_date,
        "fields": fields,
        "export": 1,
        "token": token
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        result = response.json()
        if result.get("code") == 200:
            data = result.get("data", [])
            if isinstance(data, list):
                return data
            elif isinstance(data, dict):
                return [data]
            else:
                logger.warning(f"涨停股池数据格式异常: {type(data)}")
                return []
        else:
            logger.error(f"涨停股池接口返回错误: code={result.get('code')}, msg={result.get('msg')}")
            return []
    except Exception as e:
        logger.error(f"获取涨停股池数据失败: {e}")
        return []

# 行业板块
def get_hybk_data(fields="all"):
    url = URL_GROUPS["WAIZAOWANG_HYBK_URL"]
    token = TOKEN_GROUPS["WAIZAOWANG_API_TOKEN"]
    params = {
        "code": "all",
        "fields": fields,
        "export": 1,
        "token": token
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        result = response.json()
        if result.get("code") == 200:
            data = result.get("data", [])
            if isinstance(data, list):
                return data
            elif isinstance(data, dict):
                return [data]
            else:
                logger.warning(f"行业板块数据格式异常: {type(data)}")
                return []
        else:
            logger.error(f"行业板块接口返回错误: code={result.get('code')}, msg={result.get('msg')}")
            return []
    except Exception as e:
        logger.error(f"获取行业板块数据失败: {e}")
        return []

# 每日行情
def get_daily_market_data(start_date, end_date, fields="all",code="all"):
    url = URL_GROUPS["WAIZAOWANG_MRHQ_URL"]
    token = TOKEN_GROUPS["WAIZAOWANG_API_TOKEN"]
    params = {
        "type": 1,
        "code": code,
        "startDate": start_date,
        "endDate": end_date,
        "fields": fields,
        "export": 1,
        "token": token
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        result = response.json()
        if result.get("code") == 200:
            data = result.get("data", [])
            if isinstance(data, list):
                return data
            elif isinstance(data, dict):
                return [data]
            else:
                logger.warning(f"每日行情数据格式异常: {type(data)}")
                return []
        else:
            logger.error(f"每日行情接口返回错误: code={result.get('code')}, msg={result.get('msg')}")
            return []
    except Exception as e:
        logger.error(f"获取每日行情数据失败: {e}")
        return []

# 基础信息
def get_base_info_data(fields="all"):
    url = URL_GROUPS["WAIZAOWANG_BASE_INFO_URL"]
    token = TOKEN_GROUPS["WAIZAOWANG_API_TOKEN"]
    params = {
        "code": "all",
        "fields": fields,
        "export": 1,
        "token": token,
        "type": 1
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        result = response.json()
        if result.get("code") == 200:
            data = result.get("data", [])
            if isinstance(data, list):
                return data
            elif isinstance(data, dict):
                return [data]
            else:
                logger.warning(f"基础信息数据格式异常: {type(data)}")
                return []
        else:
            logger.error(f"基础信息接口返回错误: code={result.get('code')}, msg={result.get('msg')}")
            return []
    except Exception as e:
        logger.error(f"获取基础信息数据失败: {e}")
        return []

# 日K线
def get_day_kline_data(start_date, end_date, code="all", ktype=101, fq=1, fields="all"):
    url = URL_GROUPS["WAIZAOWANG_DAY_KLINE_URL"]
    token = TOKEN_GROUPS["WAIZAOWANG_API_TOKEN"]
    params = {
        "type": 1,
        "code": code,
        "ktype": ktype,
        "fq": fq,
        "startDate": start_date,
        "endDate": end_date,
        "fields": fields,
        "export": 1,
        "token": token
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        result = response.json()
        if result.get("code") == 200:
            data = result.get("data", [])
            if isinstance(data, list):
                return data
            elif isinstance(data, dict):
                return [data]
            else:
                logger.warning(f"日K线数据格式异常: {type(data)}")
                return []
        else:
            logger.error(f"日K线接口返回错误: code={result.get('code')}, msg={result.get('msg')}")
            return []
    except Exception as e:
        logger.error(f"获取日K线数据失败: {e}")
        return []

# 交易日
def get_trade_date_data(start_date, end_date, fields="all"):
    url = URL_GROUPS["WAIZAOWANG_TRADE_DATE_URL"]
    token = TOKEN_GROUPS["WAIZAOWANG_API_TOKEN"]
    params = {
        'mtype': 1,
        "startDate": start_date,
        "endDate": end_date,
        "fields": fields,
        "export": 1,
        "token": token
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        result = response.json()
        if result.get("code") == 200:
            data = result.get("data", [])
            if isinstance(data, list):
                return data
            elif isinstance(data, dict):
                return [data]
            else:
                logger.warning(f"交易日数据格式异常: {type(data)}")
                return []
        else:
            logger.error(f"交易日接口返回错误: code={result.get('code')}, msg={result.get('msg')}")
            return []
    except Exception as e:
        logger.error(f"获取交易日数据失败: {e}")
        return []

# 年报公示
def get_nb_report_data(start_date, end_date, fields="all",code="all"):
    url = URL_GROUPS["WAIZAOWANG_NBGC_URL"]
    token = TOKEN_GROUPS["WAIZAOWANG_API_TOKEN"]
    params = {
        "startDate": start_date,
        "endDate": end_date,
        "fields": fields,
        "export": 1,
        "token": token,
        "code": code,
        'type': 1,
    }
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        result = response.json()
        if result.get("code") == 200:
            data = result.get("data", [])
            if isinstance(data, list):
                return data
            elif isinstance(data, dict):
                return [data]
            else:
                logger.warning(f"年报公示数据格式异常: {type(data)}")
                return []
        else:
            logger.error(f"年报公示接口返回错误: code={result.get('code')}, msg={result.get('msg')}")
            return []
    except Exception as e:
        logger.error(f"获取年报公示数据失败: {e}")
        return []
