# -*- coding: utf-8 -*-
# @Time 2025/5/16 18:13
# @Author: <PERSON>
"""
通用工具
"""
from datetime import datetime, timedelta
from typing import Optional

import chinese_calendar


class DateTimeManager:
    """
    时间管理器 - 单例模式
    用于统一管理系统中的当前时间，支持传参方式设置时间
    """
    _instance = None
    _current_datetime: Optional[datetime] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DateTimeManager, cls).__new__(cls)
        return cls._instance
    
    def set_current_datetime(self, dt: datetime) -> None:
        """
        设置当前时间
        
        参数:
        - dt: datetime对象，要设置的当前时间
        """
        self._current_datetime = dt
    
    def reset_to_system_time(self) -> None:
        """
        重置为系统当前时间
        """
        self._current_datetime = None
    
    def get_current_datetime(self) -> datetime:
        """
        获取当前日期时间
        
        返回:
        - datetime: 当前日期时间对象
        """
        if self._current_datetime is not None:
            return self._current_datetime
        return datetime.now()
    
    def get_current_date(self):
        """
        获取当前日期
        
        返回:
        - date: 当前日期对象
        """
        return self.get_current_datetime().date()


# 创建全局时间管理器实例
datetime_manager = DateTimeManager()


def is_trading_day(date):
    """
    判断是否为交易日

    参数:
    - date: datetime对象

    返回:
    - bool: 是否为交易日
    """
    return chinese_calendar.is_workday(date)


def get_previous_trading_day(day_ago=1, current_dt: Optional[datetime] = None):
    """
    获取前一个交易日
    
    参数:
    - day_ago: 往前推几个交易日
    - current_dt: 可选的当前时间，如果不提供则使用全局时间管理器的时间
    
    返回:
    - datetime: 前一个交易日
    """
    count = 0
    days_ago = 0
    
    # 使用传入的时间或全局时间管理器的时间
    if current_dt is not None:
        target_date = current_dt
    else:
        target_date = datetime_manager.get_current_datetime()
    
    while count < day_ago:
        # 从当前日期前一天开始计算，在每天的收盘之后开始分析
        target_date = get_previous_date(days_ago + 1, current_dt)
        if is_trading_day(target_date):
            count += 1
        days_ago += 1

    return target_date


def get_previous_date(days_ago, current_dt: Optional[datetime] = None):
    """
    获取当前日期的前几天日期

    参数:
    - days_ago: 往前推几天
    - current_dt: 可选的当前时间，如果不提供则使用全局时间管理器的时间

    返回:
    - date: 前几天的日期
    """
    # 使用传入的时间或全局时间管理器的时间
    if current_dt is not None:
        today = current_dt
    else:
        today = datetime_manager.get_current_datetime()
    
    # 计算前几天的日期，例如前3天
    previous_date = today - timedelta(days=days_ago)

    # 输出格式为 YYYY-MM-DD
    return previous_date.date()


def get_recent_trading_days(interval=0, count=7, current_dt: Optional[datetime] = None):
    """
    从某天日期向前回溯，获取最近N天的交易日

    参数:
    - interval: 回溯起始的日期与当前日期的差值，默认为当天
    - count: 获取的交易日数量，默认为一周
    - current_dt: 可选的当前时间，如果不提供则使用全局时间管理器的时间

    返回:
    - tuple: (开始日期, 结束日期)
    """
    # 使用传入的时间或全局时间管理器的时间
    if current_dt is not None:
        end_date = current_dt
    else:
        end_date = datetime_manager.get_current_datetime()
    
    # 设置成指定的结束日期
    if interval > 0:
        end_date = get_previous_date(interval, current_dt)

    trading_days = []
    current_date = end_date

    # 向前追溯，直到找到足够的交易日
    while len(trading_days) < count:
        if is_trading_day(current_date):
            trading_days.append(current_date)
        current_date -= timedelta(days=1)

    # 返回最早和最近的交易日
    return (
        trading_days[-1].strftime("%Y-%m-%d"),  # 最早的交易日
        trading_days[0].strftime("%Y-%m-%d")  # 最近的交易日
    )


def get_recent_trading_days_array(interval=0, count=3, current_dt: Optional[datetime] = None):
    """
    从某天日期向前回溯，获取最近N天的交易日

    参数:
    - interval: 回溯起始的日期与当前日期的差值，默认为当天
    - count: 获取的交易日数量，默认为3天
    - current_dt: 可选的当前时间，如果不提供则使用全局时间管理器的时间

    返回:
    - list: 交易日列表
    """
    # 使用传入的时间或全局时间管理器的时间
    if current_dt is not None:
        end_date = current_dt
    else:
        end_date = datetime_manager.get_current_datetime()
    
    # 设置成指定的结束日期
    if interval > 0:
        end_date = get_previous_date(interval, current_dt)

    trading_days = []
    current_date = end_date

    # 向前追溯，直到找到足够的交易日
    while len(trading_days) < count:
        if is_trading_day(current_date):
            trading_days.append(current_date)
        current_date -= timedelta(days=1)

    # 返回交易日列表
    return trading_days


def normalize(value, min_val, max_val):
    """将值归一化到0-1之间"""
    if max_val == min_val:
        return 0  # 避免除以零的情况
    return (float(value) - min_val) / (max_val - min_val)


if __name__ == "__main__":
    print("\n=== 使用全局时间管理器 ===")
    # 设置全局时间
    datetime_manager.set_current_datetime(datetime(2024, 5, 10, 9, 0, 0))  # 2024年5月10日 9:00
    print(datetime_manager.get_current_datetime())
    
    # 重置为系统时间
    datetime_manager.reset_to_system_time()
    print("\n=== 重置后使用系统时间 ===")
    print(datetime_manager.get_current_date())

