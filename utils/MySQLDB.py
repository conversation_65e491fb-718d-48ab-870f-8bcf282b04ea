# -*- coding: utf-8 -*-
# @Time 2025/5/17 13:32
# @Author: <PERSON>
"""
封装好的类，用于处理MySQL数据库的各类操作
"""
from typing import List, Dict, Union, Sequence, Any

import pymysql

from config.constant import get_mysql_database_config


class MySQLDB:
    def __init__(self,
                 host: str,
                 user: str,
                 password: str,
                 database: str,
                 port: int = 3306,
                 charset: str = 'utf8mb4',
                 **kwargs):
        """
        初始化MySQL数据库连接
        :param host: 数据库地址
        :param user: 用户名
        :param password: 密码
        :param database: 数据库名称
        :param port: 端口号，默认3306
        :param charset: 字符编码，默认utf8mb4
        :param kwargs: 其他pymysql连接参数
        """
        self.conn_params = {
            'host': host,
            'user': user,
            'password': password,
            'database': database,
            'port': port,
            'charset': charset,
            **kwargs
        }
        self.connection = None
        self.autocommit = False

    def __enter__(self):
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = pymysql.connect(**self.conn_params)
            self.connection.autocommit = self.autocommit  # type: ignore
        except pymysql.Error as e:
            raise ConnectionError(f"数据库连接失败: {e}")

    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None

    def execute_ddl(self, sql: str) -> bool:
        """
        执行DDL语句（建表、删表等操作）
        :param sql: DDL语句
        :return: 执行是否成功
        """
        assert self.connection is not None, "数据库未连接"
        try:
            with self.connection.cursor() as cursor:  # type: ignore
                cursor.execute(sql)
                if not self.autocommit:
                    self.connection.commit()  # type: ignore
                return True
        except pymysql.Error as e:
            self.connection.rollback()  # type: ignore
            raise RuntimeError(f"DDL执行失败: {e}")

    def execute_query(self,
                      sql: str,
                      params: Union[tuple, dict, None] = None) -> Any:
        """
        执行查询操作（SELECT）
        :param sql: SQL查询语句
        :param params: 查询参数
        :return: 结果字典列表
        """
        assert self.connection is not None, "数据库未连接"
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:  # type: ignore
                cursor.execute(sql, params)
                result = cursor.fetchall()
                if isinstance(result, list):
                    return result
                else:
                    return list(result)
        except pymysql.Error as e:
            raise RuntimeError(f"查询执行失败: {e}")

    def execute_write(self,
                      sql: str,
                      params: Union[tuple, dict, Sequence[Union[tuple, dict]]] = () ) -> int:
        """
        执行写操作（INSERT/UPDATE/DELETE）
        :param sql: SQL语句
        :param params: 参数（支持单条或批量，支持Sequence类型）
        :return: 受影响的行数
        """
        assert self.connection is not None, "数据库未连接"
        try:
            with self.connection.cursor() as cursor:  # type: ignore
                if isinstance(params, (list, tuple)) and not isinstance(params, dict):
                    rows = cursor.executemany(sql, params)
                else:
                    rows = cursor.execute(sql, params)

                if not self.autocommit:
                    self.connection.commit()  # type: ignore
                return rows or 0
        except pymysql.Error as e:
            self.connection.rollback()  # type: ignore
            raise RuntimeError(f"写操作失败: {e}")

    def create_table(self,
                     table_name: str,
                     columns: List[str],
                     if_not_exists: bool = True) -> bool:
        """
        创建数据表
        :param table_name: 表名
        :param columns: 列定义列表
        :param if_not_exists: 是否使用IF NOT EXISTS
        :return: 是否成功
        """
        if_not_exists_clause = "IF NOT EXISTS" if if_not_exists else ""
        columns_sql = ", ".join(columns)
        sql = f"CREATE TABLE {if_not_exists_clause} {table_name} ({columns_sql})"

        return self.execute_ddl(sql)

    def drop_table(self,
                   table_name: str,
                   if_exists: bool = True) -> bool:
        """
        删除数据表
        :param table_name: 表名
        :param if_exists: 是否使用IF EXISTS
        :return: 是否成功
        """
        if_exists_clause = "IF EXISTS" if if_exists else ""
        sql = f"DROP TABLE {if_exists_clause} {table_name}"
        return self.execute_ddl(sql)

    def set_autocommit(self, mode: bool):
        """设置自动提交模式"""
        self.autocommit = mode
        if self.connection:
            self.connection.autocommit = mode  # type: ignore

    def start_transaction(self):
        """开启事务"""
        self.set_autocommit(False)

    def commit(self):
        """提交事务"""
        if self.connection:
            self.connection.commit()

    def rollback(self):
        """回滚事务"""
        if self.connection:
            self.connection.rollback()


# 使用示例
if __name__ == "__main__":
    # 数据库配置
    config = get_mysql_database_config()

    try:
        with MySQLDB(**config) as db:
            # 创建表
            db.create_table(
                table_name="employees",
                columns=[
                    "id INT AUTO_INCREMENT PRIMARY KEY",
                    "name VARCHAR(255) NOT NULL",
                    "email VARCHAR(255) UNIQUE",
                    "salary DECIMAL(10,2)",
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
                ]
            )

            # 插入数据
            insert_sql = "INSERT INTO employees (name, email, salary) VALUES (%s, %s, %s)"
            print("插入行数:", db.execute_write(insert_sql, ("Alice", "<EMAIL>", 8500.00)))

            # 批量插入
            batch_data = [
                ("Bob", "<EMAIL>", 9200.00),
                ("Charlie", "<EMAIL>", 7800.00)
            ]
            print("批量插入行数:", db.execute_write(insert_sql, batch_data))

            result = db.execute_query("SELECT * FROM employees")

            # 查询数据
            results = db.execute_query("SELECT * FROM employees WHERE salary > %s", (8000,))
            print("高薪员工:")
            for row in results:
                print(row)

            # 更新数据
            update_sql = "UPDATE employees SET salary = %s WHERE name = %s"
            print("更新行数:", db.execute_write(update_sql, (9500.00, "Alice")))

            # 删除数据
            delete_sql = "DELETE FROM employees WHERE email = %s"
            print("删除行数:", db.execute_write(delete_sql, ("<EMAIL>",)))

            # 删除表
            db.drop_table("employees")

    except Exception as e:
        print(f"数据库操作出错: {e}")
    finally:
        db.close()
