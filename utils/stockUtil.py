# -*- coding: utf-8 -*-
# @Time 2025/5/16 20:52
# @Author: <PERSON>
"""
股票分析工具
"""
import traceback

import pandas as pd

from config.constant import URL_GROUPS, TOKEN_GROUPS, MAX_ORDER_NUMBER, STOCK_GROUPS
from config.logConfig import logger
from utils.commonUtil import get_previous_date, is_trading_day, normalize
from utils.httpUtil import  get_daily_market_data


def create_daily_stock_count_report(df, situation="DOWN"):
    """
    创建每日跌停股票数量统计报告

    参数:
    - df: 包含跌停/涨停股票数据的DataFrame
    - situation: 跌停或者涨停标志

    返回:
    - DataFrame: 包含日期和跌停/涨停股票数量的DataFrame
    """
    try:
        # 确保tdate列是日期时间类型
        if not pd.api.types.is_datetime64_any_dtype(df['tdate']):
            df['tdate'] = pd.to_datetime(df['tdate'])

        # 计算每个交易日的股票数量
        daily_counts = df.groupby(df['tdate'].dt.date)['code'].nunique().reset_index()

        # 重命名列
        daily_counts.columns = ['交易日期', '跌停股票数量']
        if situation == STOCK_GROUPS["STOCK_SITUATION_UP"]:
            daily_counts.columns = ['交易日期', '涨停股票数量']

        # 将日期转换为字符串格式，便于阅读
        daily_counts['交易日期'] = daily_counts['交易日期'].astype(str)

        return daily_counts
    except Exception as e:
        logger.error(f"创建每日跌停股票数量统计报告时发生错误: {e}")
        logger.error(traceback.format_exc())
        return None


def get_sentiment_index_statistic(days=30):
    """
    获取N天内股市所有股票的统计数据
    @param days:
    @return: 指定周期内统计指标的最大最小值
    """
    count = 0
    days_ago = 0
    df = pd.DataFrame()
    while count < days:
        # 从当前日期开始计算，在每天的收盘之后开始分析
        end_date = get_previous_date(days_ago)
        if is_trading_day(end_date):
            res = get_daily_market_data(start_date=end_date, end_date=end_date,code="all", fields="code,name,tdate,price,cje,cjl,hslv,zrspj,zljlr,zdfd,zded,z197,z50")
            res = pd.DataFrame(res)
            df = pd.concat([df, res], axis=0)
            count += 1
        days_ago += 1
    # 情绪计算因子成交量
    cje_max = df.groupby("code")["cje"].max()
    cje_max = pd.DataFrame(cje_max)
    cje_max = cje_max.rename(columns={"cje": "cje_max"})
    cje_min = df.groupby("code")["cje"].min()
    cje_min = pd.DataFrame(cje_min)
    cje_min = cje_min.rename(columns={"cje": "cje_min"})
    # 情绪因子价格波动
    price_max = df.groupby("code")["price"].max()
    price_max = pd.DataFrame(price_max)
    price_max = price_max.rename(columns={"price": "price_max"})
    price_min = df.groupby("code")["price"].min()
    price_min = pd.DataFrame(price_min)
    price_min = price_min.rename(columns={"price": "price_min"})
    # 情绪计算因子主力净流入
    zljlr_max = df.groupby("code")["zljlr"].max()
    zljlr_max = pd.DataFrame(zljlr_max)
    zljlr_max = zljlr_max.rename(columns={"zljlr": "zljlr_max"})
    zljlr_min = df.groupby("code")["zljlr"].min()
    zljlr_min = pd.DataFrame(zljlr_min)
    zljlr_min = zljlr_min.rename(columns={"zljlr": "zljlr_min"})
    # 情绪计算因子换手率
    hslv_max = df.groupby("code")["hslv"].max()
    hslv_max = pd.DataFrame(hslv_max)
    hslv_max = hslv_max.rename(columns={"hslv": "hslv_max"})
    hslv_min = df.groupby("code")["hslv"].min()
    hslv_min = pd.DataFrame(hslv_min)
    hslv_min = hslv_min.rename(columns={"hslv": "hslv_min"})

    index_statistic = pd.concat([cje_max, cje_min, price_max, price_min, zljlr_max, zljlr_min, hslv_max, hslv_min], axis=1)

    return index_statistic


def calculate_sentiment_index(cjl, zrspj, zrspj2, zljlr, hs, market_env_value):
    """
    计算情绪指标，包含市场环境变量（带权重0.3）
    公式: 0.25*cjl成交量 + 0.1*(zrspj-zrspj2)价格变动 + 0.15*zljlr主力净流入 + 0.2*hs换手率 + 0.3*market_env_value市场情绪
    """
    price_change = zrspj - zrspj2  # 价格变化可能为负值

    # 分别计算各个组成部分
    cjl_component = 0.25 * cjl
    price_component = 0.1 * price_change
    zljlr_component = 0.15 * zljlr
    hs_component = 0.2 * hs
    market_component = 0.3 * market_env_value  # 注意这里乘以0.3

    # 最终指标 = 各分量之和
    sentiment_index = cjl_component + price_component + zljlr_component + hs_component + market_component

    # 返回总值和各个分量
    return {
        'total': sentiment_index,
        'cjl_component': cjl_component,
        'price_component': price_component,
        'zljlr_component': zljlr_component,
        'hs_component': hs_component,
        'market_component': market_component,
        'price_change': price_change
    }


def calculate_sentiment_index_by_statistic_data(daily_data, index_statistic, market_env_value):
    """
    根据统计信息计算情绪维度指标
    @param market_env_value: 市场判断值
    @param daily_data: 当天股市行情数据
    @param index_statistic: 前N天股市统计情况
    @return:
    """
    # 存储所有股票的情绪指标和详细数据，用于后续生成CSV
    all_stocks_data = []

    combine_date = pd.merge(daily_data, index_statistic, on="code")

    for i, rows in combine_date.iterrows():
        # 提取原始值
        cjl_raw = float(rows["cje"])
        price_raw = float(rows["price"])
        zrspj_raw = float(rows["zrspj"])
        zljlr_raw = float(rows["zljlr"])
        hs_raw = float(rows["hslv"])

        # 归一化值
        cjl = normalize(cjl_raw, float(rows['cje_min']), float(rows["cje_max"]))
        price = normalize(price_raw, float(rows['price_min']), float(rows["price_max"]))
        zrspj = normalize(zrspj_raw, float(rows['price_min']), float(rows["price_max"]))
        zljlr = normalize(zljlr_raw, float(rows['zljlr_min']), float(rows["zljlr_max"]))
        hs = normalize(hs_raw, float(rows['hslv_min']), float(rows["hslv_max"]))

        # 计算情绪指标（包含市场环境变量）
        sentiment_components = calculate_sentiment_index(cjl, price, zrspj, zljlr, hs, market_env_value)
        sentiment_index = sentiment_components['total']

        stock_data = {
            # 提取的原始数据
            'code': rows["code"],
            'tdate': rows["tdate"],  # 添加交易时间
            'name': rows["name"],
            'zdfd': float(rows["zdfd"]),
            'zded': float(rows["zded"]),
            'z197': rows["z197"],
            'z50': rows["z50"],
            

            # 处理中间过程的原始数据
            'cjl_raw': cjl_raw,
            'price_raw': price_raw,
            'zrspj_raw': zrspj_raw,
            'zljlr_raw': zljlr_raw,
            'hs_raw': hs_raw,

            # 计算指标的中间过程
            'price_change': sentiment_components['price_change'],
            'cjl_component': sentiment_components['cjl_component'],
            'price_component': sentiment_components['price_component'],
            'zljlr_component': sentiment_components['zljlr_component'],
            'hs_component': sentiment_components['hs_component'],
            'market_component': sentiment_components['market_component'],

            # 最终结果
            'sentiment_index': sentiment_index
        }

        all_stocks_data.append(stock_data)

    if all_stocks_data:
        # 按情绪指标排序
        all_stocks_data.sort(key=lambda x: x['sentiment_index'], reverse=True)

        # 只选取排序前N个入库
        if len(all_stocks_data) > MAX_ORDER_NUMBER:
            all_stocks_data = all_stocks_data[:MAX_ORDER_NUMBER]

        return all_stocks_data


def get_average_cjl_statistic(days=5):
    """
    统计近5个交易日的成交量，用于计算量能比率
    @param days:
    @return:
    """
    count = 0
    days_ago = 0
    df = pd.DataFrame()
    while count < days:
        # 从当前日期前一天开始计算，在每天的收盘之后开始分析
        end_date = get_previous_date(days_ago)
        if is_trading_day(end_date):
            res = get_daily_market_data(start_date=end_date, end_date=end_date,fields = "code,name,tdate,cjl")
            res = pd.DataFrame(res)
            df = pd.concat([df, res], axis=0)
            count += 1
        days_ago += 1

    five_cjl_df = df.groupby("code")["cjl"].mean()
    five_cjl_df = pd.DataFrame(five_cjl_df)
    five_cjl_df = five_cjl_df.rename(columns={"cjl": "cjl_5_average"})

    # 返回五日成交量均值统计
    return five_cjl_df


def get_price_average_statistic(days=5): 
    """
    获取股价五日均线统计
    @param days:
    @return:
    """
    count = 0
    days_ago = 0
    df = pd.DataFrame()
    while count < days:
        # 从当前日期前一天开始计算，在每天的收盘之后开始分析
        end_date = get_previous_date(days_ago)
        if is_trading_day(end_date):
            res = get_daily_market_data(start_date=end_date, end_date=end_date,code="all", fields="code,name,tdate,price")
            res = pd.DataFrame(res)
            df = pd.concat([df, res], axis=0)
            count += 1
        days_ago += 1

    # print(df.head(5))
    five_price_df = df.groupby("code")["price"].mean()
    five_price_df = pd.DataFrame(five_price_df)
    five_price_df = five_price_df.rename(columns={"price": "price_5_average"})

    # 返回五日价格均值统计
    return five_price_df

def assign_stars(data_list):
    n = len(data_list)
    if n == 0:
        return []
    
    # 计算各星级区间的元素数量（四舍五入）
    star5 = round(0.05 * n)
    star4 = round(0.15 * n)
    star3 = round(0.45 * n)
    star2 = round(0.25 * n)
    star1 = n - (star5 + star4 + star3 + star2)  # 剩余元素归为最后10%
    
    # 遍历列表并分配星级
    current_idx = 0
    for i in range(star5):
        if current_idx < n:
            data_list[current_idx]['star_lev'] = 5
            current_idx += 1
    for i in range(star4):
        if current_idx < n:
            data_list[current_idx]['star_lev'] = 4
            current_idx += 1
    for i in range(star3):
        if current_idx < n:
            data_list[current_idx]['star_lev'] = 3
            current_idx += 1
    for i in range(star2):
        if current_idx < n:
            data_list[current_idx]['star_lev'] = 2
            current_idx += 1
    for i in range(star1):
        if current_idx < n:
            data_list[current_idx]['star_lev'] = 1
            current_idx += 1
    return data_list

if __name__ == "__main__":
    # get_average_cjl_statistic()

    res = get_price_average_statistic()
    print(res.head(5))
