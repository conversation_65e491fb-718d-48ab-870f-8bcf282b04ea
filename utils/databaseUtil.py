# -*- coding: utf-8 -*-
# @Time 2025/5/17 5:15
# @Author: <PERSON>
"""
数据库连接相关工具
"""
import pandas as pd
from typing import List, Dict, Any
from config.constant import get_mysql_database_config
from config.logConfig import logger
from utils.MySQLDB import MySQLDB


def get_database_connection(config):
    """
    获取MySQLDB数据库连接实例
    @param config: 数据库配置信息
    @return: MySQLDB实例
    """
    try:
        db = MySQLDB(**config)
        db.connect()
        return db
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def create_table_if_not_exist(config, table_name, columns=None, charset="utf8mb4"):
    """
    创建表
    @param config: 数据库配置信息
    @param charset:
    @param table_name:
    @param columns: 表列的定义
    @return:
    """
    if columns is None:
        columns = []
    db = None
    try:
        db = MySQLDB(**config)
        db.connect()
        res = db.create_table(table_name=table_name, columns=columns, if_not_exists=True)
        logger.info(f"创建表{table_name}成功")
        return res
    except Exception as e:
        logger.error(f"数据库操作出错: {e}")
    finally:
        if db:
            db.close()

def drop_table_if_exist(config, table_name):
    """
    删除指定表名
    @param config:
    @param table_name:
    @return:
    """
    db = None
    try:
        db = MySQLDB(**config)
        db.connect()
        res = db.drop_table(table_name=table_name, if_exists=True)
        logger.info(f"删除表{table_name}成功")
        return res
    except Exception as e:
        logger.error(f"数据库操作出错: {e}")
    finally:
        if db:
            db.close()

def insert_into_table(config, data: List[Dict[str, Any]], table_name: str, columns: List[str]):
    """
    插入数据库，支持批量插入
    @param config:
    @param data:
    @param table_name:
    @param columns: 列名
    @return:
    """
    db = None
    try:
        db = MySQLDB(**config)
        db.connect()
        insert_sql = f"""
            INSERT INTO {table_name} ({', '.join([f'`{col}`' for col in columns])})
            VALUES ({', '.join(['%s'] * len(columns))});
            """
        values = [tuple(item[col] for col in columns) for item in data]
        res = db.execute_write(insert_sql, values)
        logger.info(f"插入表{table_name}成功")
        return res
    except Exception as e:
        logger.error(f"数据库操作出错: {e}")
    finally:
        if db:
            db.close()

def create_table_and_insert_data(data, table_name, config):
    """
    创建表并插入数据，全部基于MySQLDB实现
    @param data:
    @param table_name:
    @param config: 数据库配置信息
    @return:
    """
    if not data:
        print("数据为空，无需操作。")
        return
    columns = [
        'code',
        'name',
        'star_lev',
        'star_type',
        'industry',
        'concept',
        'insert_time',
    ]
    db = None
    try:
        db = MySQLDB(**config)
        db.connect()
        # 假设表结构已知，如需动态建表可扩展
        insert_sql = f"""
            INSERT INTO {table_name} ({', '.join([f'`{col}`' for col in columns])})
            VALUES ({', '.join(['%s']*len(columns))});
        """
        values = [tuple(item[col] for col in columns) for item in data]
        db.execute_write(insert_sql, values)
        print(f"插入 {len(data)} 条数据成功！")
    except Exception as e:
        print(f"操作失败: {e}")
        if db:
            db.rollback()
    finally:
        if db:
            db.close()

def delete_update_table(config, sql, data, table_name):
    """
    删除或更新表，支持批量
    @param config:
    @param sql:
    @param data:
    @param table_name:
    @return:
    """
    db = None
    try:
        db = MySQLDB(**config)
        db.connect()
        res = db.execute_write(sql, data)
        logger.info(f"删除或者更新表{table_name}成功")
        return res
    except Exception as e:
        logger.error(f"数据库操作出错: {e}")
    finally:
        if db:
            db.close()

def query_table(config, table_name, sql):
    """
    查询表
    @param sql:
    @param config:
    @param table_name:
    @return:
    """
    db = None
    try:
        db = MySQLDB(**config)
        db.connect()
        res = db.execute_query(sql)
        logger.info(f"查询表{table_name}成功")
        return res
    except Exception as e:
        logger.error(f"数据库操作出错: {e}")
    finally:
        if db:
            db.close()

if __name__ == "__main__":
    db_config = get_mysql_database_config()
    print(query_table(db_config, "test_table", "SELECT * FROM test_table"))


