# Utils 工具模块

本模块提供了股票分析系统的核心工具类，包含HTTP请求封装、时间管理、数据库操作和股票分析工具等功能。

## 📁 文件结构

```
utils/
├── README.MD          # 本文件
├── httpUtil.py        # HTTP请求工具 - 封装WAIZAO网API
├── commonUtil.py      # 通用工具 - 时间管理器等
├── databaseUtil.py    # 数据库操作工具
├── MySQLDB.py         # MySQL数据库连接类
└── stockUtil.py       # 股票分析工具
```

## 🚀 核心功能

### 1. HTTP请求工具 (`httpUtil.py`)

**功能描述**: 将WAIZAO网项目中用到的所有URL封装成了方法，并通过传参来达到不同的数据获取要求。

**主要特性**:
- 统一封装WAIZAO网API接口
- 支持参数化配置，灵活获取不同数据
- 完善的错误处理和日志记录
- 自动处理API响应格式

**包含的API方法**:
- `get_dtgc_data()` - 跌停股池数据
- `get_ztgc_data()` - 涨停股池数据  
- `get_hybk_data()` - 行业板块数据
- `get_daily_market_data()` - 每日行情数据
- `get_base_info_data()` - 基础信息数据
- `get_day_kline_data()` - 日K线数据
- `get_trade_date_data()` - 交易日数据
- `get_nb_report_data()` - 年报数据

**使用示例**:
```python
from utils.httpUtil import get_daily_market_data

# 获取指定日期范围的每日行情数据
data = get_daily_market_data(
    start_date="2024-01-01", 
    end_date="2024-01-31",
    fields="code,name,price,volume"
)
```

### 2. 时间管理器 (`commonUtil.py`)

**功能描述**: 管理了一个单例模式的时间管理器，可以调整他本身的时间来进行灵活的debug。

**主要特性**:
- 单例模式设计，全局统一时间管理
- 支持自定义时间设置，便于调试和测试
- 交易日判断功能
- 灵活的日期计算工具

**核心类和方法**:
- `DateTimeManager` - 单例时间管理器
  - `set_current_datetime()` - 设置当前时间
  - `reset_to_system_time()` - 重置为系统时间
  - `get_current_datetime()` - 获取当前时间
- `is_trading_day()` - 判断是否为交易日
- `get_previous_trading_day()` - 获取前N个交易日
- `get_recent_trading_days()` - 获取最近N个交易日

**使用示例**:
```python
from utils.commonUtil import datetime_manager, get_previous_trading_day

# 设置自定义时间进行调试
from datetime import datetime
datetime_manager.set_current_datetime(datetime(2024, 1, 15))

# 获取前一个交易日
prev_trading_day = get_previous_trading_day(1)

# 重置为系统时间
datetime_manager.reset_to_system_time()
```

### 3. 数据库操作工具 (`databaseUtil.py` & `MySQLDB.py`)

**功能描述**: 数据库相关的两个文件主要是预留的，以方便后续对数据库操作的拓展。目前由于5星排序是往一张表上写，所以使用一个方法一套流程完成了数据入库。选股还没有完全用到这里的数据库操作并没有很好的解耦合。

**主要特性**:
- 统一的数据库连接管理
- 支持表的创建、删除、插入、查询等操作
- 批量数据处理能力
- 事务支持

**核心方法**:
- `get_database_connection()` - 获取数据库连接
- `create_table_if_not_exist()` - 创建表
- `insert_into_table()` - 批量插入数据
- `create_table_and_insert_data()` - 创建表并插入数据（五星排行专用）
- `query_table()` - 查询表数据
- `delete_update_table()` - 删除或更新数据

**使用示例**:
```python
from utils.databaseUtil import create_table_and_insert_data
from config.constant import get_mysql_database_config

# 五星排行数据入库
config = get_mysql_database_config()
create_table_and_insert_data(
    data=stock_data_list,
    table_name="five_star_ranking",
    config=config
)
```

### 4. 股票分析工具 (`stockUtil.py`)

**功能描述**: 股票分析工具，应该可以处理常见的股票指标。目前五星排行中有一些模块用到了，有一些模块的股票工具类方法暂时没有抽象到这个模块。主要原因是因为股票分析工具与股票业务深度相关，目前没有足够的知识储备去改业务逻辑，需要根据需求文档统一规划可以复用的股票分析工具。

**主要特性**:
- 股票情绪指标计算
- 每日股票数量统计
- 市场环境分析
- 数据归一化处理

**核心方法**:
- `create_daily_stock_count_report()` - 创建每日股票数量统计报告
- `get_sentiment_index_statistic()` - 获取情绪指标统计数据
- `calculate_sentiment_index()` - 计算情绪指标
- `calculate_sentiment_index_by_statistic_data()` - 基于统计数据计算情绪指标
- `get_average_cjl_statistic()` - 获取平均成交量统计
- `get_price_average_statistic()` - 获取平均价格统计
- `assign_stars()` - 星级分配

**情绪指标计算公式**:
```
情绪指标 = 0.25×成交量 + 0.1×价格变动 + 0.15×主力净流入 + 0.2×换手率 + 0.3×市场环境
```

**使用示例**:
```python
from utils.stockUtil import calculate_sentiment_index_by_statistic_data

# 计算股票情绪指标
result = calculate_sentiment_index_by_statistic_data(
    daily_data=daily_market_data,
    index_statistic=historical_statistics,
    market_env_value=market_environment_score
)
```

## 🔧 依赖项

- `pandas` - 数据处理
- `requests` - HTTP请求
- `chinese_calendar` - 中国交易日历
- `mysql-connector-python` - MySQL数据库连接

## 📝 注意事项

1. **时间管理**: 使用时间管理器时，注意在调试完成后重置为系统时间
2. **数据库操作**: 目前数据库操作主要用于五星排行功能，其他模块的解耦工作待后续完善
3. **股票分析工具**: 部分功能与业务逻辑耦合较深，需要根据具体需求进行重构
4. **错误处理**: 所有工具类都包含完善的错误处理和日志记录

## 🚧 待优化项目

1. **数据库解耦**: 将选股模块的数据库操作迁移到统一的数据库工具类
2. **股票分析工具重构**: 根据需求文档统一规划可复用的股票分析工具
3. **API接口优化**: 增加更多的参数验证和错误处理
4. **性能优化**: 对大数据量处理进行性能优化

## 📞 联系方式

如有问题或建议，请联系开发团队。
