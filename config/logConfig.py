# -*- coding: utf-8 -*-
# @Time 2025/5/16 19:32
# @Author: <PERSON>
"""
日志相关
"""
import logging
import sys
from logging.handlers import RotatingFileHandler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        # logging.FileHandler('stock_sentiment_analyse.log', encoding='utf-8'),
        # 设置日志的最大长度
        RotatingFileHandler('stock_dingshi.log', maxBytes=50*1024*1024, backupCount=5, encoding="utf-8")
    ]
)
logger = logging.getLogger(__name__)
