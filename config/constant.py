# -*- coding: utf-8 -*-
# @Time 2025/5/16 18:23
# @Author: <PERSON>
"""
各类不变的量
"""
# 排序入库最大值
MAX_ORDER_NUMBER = 60

# 所有的API调用token
TOKEN_GROUPS = {
    "WAIZAOWANG_API_TOKEN": "113cd8eb9b8fd76185c9cf1b5d6411dd"
}

# 金融数据请求URL地址
URL_GROUPS = {
    # 跌停接口URL
    "WAIZAOWANG_DTGC_URL": "http://api.waizaowang.com/doc/getPoolDT",
    # 涨停接口URL
    "WAIZAOWANG_ZTGC_URL": "http://api.waizaowang.com/doc/getPoolZT",
    # 行业板块接口URL
    "WAIZAOWANG_HYBK_URL": "http://api.waizaowang.com/doc/getStockHyBKBaseInfo",
    # 每日行情接口URL
    "WAIZAOWANG_MRHQ_URL": "http://api.waizaowang.com/doc/getDailyMarket",
    "WAIZAOWANG_BASE_INFO_URL" : "http://api.waizaowang.com/doc/getBaseInfo",
    "WAIZAOWANG_DAY_KLINE_URL" : "http://api.waizaowang.com/doc/getDayKLine",
    "WAIZAOWANG_TRADE_DATE_URL": "http://api.waizaowang.com/doc/getTradeDate",
    "WAIZAOWANG_NBGC_URL": "http://api.waizaowang.com/doc/getReportNianBao"
}


# 股票涨跌标志
STOCK_GROUPS = {
    "STOCK_SITUATION_DOWN": "DOWN",
    "STOCK_SITUATION_UP": "UP"
}

# 数据配置信息
DATABASE_GROUPS = {
    'host': '**************',
    'user': 'root',
    'password': '631c4d38efa7877c',
    'database': 'stock_learn',
    'port': 3306,
    'charset': 'utf8mb4'
}

# 表名列表
TBALE_NAME_GROUPS = {
    "SENTIMENT_TABLE_NAME": "sentiment_stock_analysis",
    "LJ_TABLE_NAME": "lj_stock_analysis"
}

def get_mysql_database_config():
    """
    获取MySQL数据库配置
    @return:
    """
    return {
        "host": DATABASE_GROUPS["host"],
        "user": DATABASE_GROUPS["user"],
        "password": DATABASE_GROUPS["password"],
        "database": DATABASE_GROUPS["database"],
        "port": DATABASE_GROUPS["port"],
        "charset": DATABASE_GROUPS["charset"]
    }
