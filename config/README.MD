# 配置文件目录

本目录包含股票评估系统的各种配置文件。

## 文件说明

### `constant.py`
- **作用**: 系统常量配置
- **内容**: 
  - API调用token配置
  - 金融数据请求URL地址
  - 数据库连接配置
  - 表名配置
  - 股票涨跌标志等常量

### `logConfig.py`
- **作用**: 日志配置
- **功能**: 
  - 配置日志级别为INFO
  - 同时输出到控制台和文件
  - 使用RotatingFileHandler进行日志轮转
  - 日志文件最大50MB，保留5个备份

### `tableConfig.py`
- **作用**: 数据库表结构定义
- **内容**:
  - 情绪维度分析表结构 (`sentiment_table_columns`)
  - 量价维度分析表结构 (`lj_table_columns`)
  - 测试表结构 (`test_table_columns`)
  - 对应的插入字段配置

### `__init__.py`
- **作用**: Python包初始化文件

## 使用说明

1. 修改数据库配置：编辑 `constant.py` 中的 `DATABASE_GROUPS`
2. 调整日志配置：修改 `logConfig.py` 中的日志级别和文件路径
3. 更新表结构：在 `tableConfig.py` 中修改表字段定义

## 注意事项

- 请妥善保管API token等敏感信息
- 数据库配置信息包含密码，注意安全性
- 日志文件会自动轮转，避免占用过多磁盘空间
