# -*- coding: utf-8 -*-
# @Time 2025/5/17 14:38
# @Author: <PERSON>
"""
表定义
"""

# 情绪维度分析表定义，用于创建表
sentiment_table_columns = [
    "id INT AUTO_INCREMENT PRIMARY KEY",
    "code VARCHAR(10)",
    "price DECIMAL(10,2)",
    "zdfd DECIMAL(10,2)",
    "zded DECIMAL(10,2)",
    "name VARCHAR(50)",
    "`rank` INT",
    "date VARCHAR(20)",
    "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
]

sentiment_table_insert_columns = [
    'rank',
    'code',
    'name',
    'price',
    'zdfd',
    'zded',
    'date'
]

# 量价维度分析表定义，用于创建表
lj_table_columns = [
    "id INT AUTO_INCREMENT PRIMARY KEY",
    "code VARCHAR(10)",
    "price DECIMAL(10,2)",
    "zdfd DECIMAL(10,2)",
    "zded DECIMAL(10,2)",
    "name VARCHAR(50)",
    "`rank` INT",
    "date VARCHAR(20)",
    "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
]

lj_table_insert_columns = [
    'rank',
    'code',
    'name',
    'price',
    'zdfd',
    'zded',
    'date'
]

"""
测试用表定义
"""
test_table_columns = [
    "id INT AUTO_INCREMENT PRIMARY KEY",
    "code VARCHAR(10)",
    "price DECIMAL(10,2)",
    "zdfd DECIMAL(10,2)",
    "zded DECIMAL(10,2)",
    "name VARCHAR(50)",
    "`rank` INT",
    "date VARCHAR(20)",
    "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
]

test_table_insert_columns = [
    'rank',
    'code',
    'name',
    'price',
    'zdfd',
    'zded',
    'date'
]
