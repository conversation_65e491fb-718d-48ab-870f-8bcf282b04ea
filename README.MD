# 股票数据分析与选股系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Active-brightgreen.svg)]()
[![Performance](https://img.shields.io/badge/Performance-90%2B%25%20Optimized-orange.svg)]()

## 📋 项目简介

这是一个专业的股票数据分析与选股系统，通过定时任务自动获取股票数据，进行多维度分析，生成5星模型排行，并提供智能选股功能。系统采用模块化设计，支持多种技术指标计算和情感分析，为投资决策提供数据支持。

## 🎯 核心亮点

### 🏗️ 架构优化 - 高内聚低耦合设计
- **业务与数据解耦**: 已较好地将业务逻辑和数据处理进行了解耦，遵从高内聚低耦合及开放封闭原则对代码进行了整体重构
- **模块化设计**: 将不同功能模块独立封装，便于维护和扩展
- **开放封闭原则**: 系统设计支持功能扩展而无需修改现有代码

### ⚡ 性能大幅提升 - 90%+ 优化
- **线程优化**: 在 `jbm_stock_analysis.py` 和 `SelectStock_win.py` 等核心文件中取消了多线程处理
- **IO访问优化**: 主要通过减少IO访问量，一次性获取数据在DataFrame中操作，大幅提升执行效率
- **整体性能提升**: 系统整体执行速度优化了**90%以上**

### 📊 多维度分析
- **量价分析**: 成交量与价格关系深度分析
- **情绪分析**: 市场情绪指标综合评估  
- **题材分析**: 热点题材和概念识别
- **技术分析**: 多种技术指标计算
- **基本面分析**: 财务指标和基本面数据

## 🏗️ 项目结构

```
stock-evaluation/
├── config/                 # 配置文件目录
│   ├── constant.py         # 常量定义
│   ├── logConfig.py        # 日志配置
│   └── tableConfig.py      # 数据库表配置
├── utils/                  # 工具类目录
│   ├── commonUtil.py       # 通用工具函数
│   ├── stockUtil.py        # 股票相关工具
│   ├── httpUtil.py         # HTTP请求工具
│   ├── databaseUtil.py     # 数据库操作工具
│   └── MySQLDB.py          # MySQL数据库连接
├── stockAnalysis/          # 股票分析模块
│   ├── SelectStock_win.py  # 选股策略 (性能优化核心)
│   ├── jbm_stock_analysis.py      # 基本面分析 (性能优化核心)
│   ├── jbm_js_stock_analysis.py   # 技术面分析
│   ├── ticai.py            # 题材分析
│   ├── quantityPriceAspect.py     # 量价分析
│   └── emotionalAspect.py  # 情感分析
├── main.py                 # 程序入口
├── requirements.txt        # 依赖包列表
└── README.MD              # 项目说明文档
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- MySQL 数据库
- 网络连接（用于获取股票数据）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/stock-evaluation.git
   cd stock-evaluation
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置数据库**
   - 在 `config/` 目录下配置数据库连接信息
   - 确保MySQL服务正常运行

4. **配置日志**
   - 根据需要修改 `config/logConfig.py` 中的日志配置

### 运行程序

```bash
python main.py
```

程序将在每天下午6:50自动执行分析任务（仅限交易日）。

## 📁 模块说明

### Config 配置模块
- **constant.py**: 定义系统常量，如API地址、数据库配置等
- **logConfig.py**: 日志配置，支持不同级别的日志输出
- **tableConfig.py**: 数据库表结构配置

### Utils 工具模块
- **commonUtil.py**: 通用工具函数，包含时间管理、日期判断等
- **stockUtil.py**: 股票相关工具，包含技术指标计算、数据处理等
- **httpUtil.py**: HTTP请求工具，用于获取股票数据
- **databaseUtil.py**: 数据库操作工具，提供数据增删改查功能
- **MySQLDB.py**: MySQL数据库连接和操作封装

### StockAnalysis 分析模块
- **SelectStock_win.py**: 选股策略实现，基于多维度指标筛选优质股票
- **jbm_stock_analysis.py**: 基本面分析，包含财务指标分析
- **jbm_js_stock_analysis.py**: 技术面分析，包含各种技术指标
- **ticai.py**: 题材分析，识别热点概念和题材
- **quantityPriceAspect.py**: 量价分析，分析成交量与价格关系
- **emotionalAspect.py**: 情感分析，分析市场情绪指标

## ⚙️ 配置说明

### 数据库配置
在 `config/constant.py` 中配置数据库连接信息：
```python
# 数据库配置
DB_HOST = 'localhost'
DB_PORT = 3306
DB_USER = 'your_username'
DB_PASSWORD = 'your_password'
DB_NAME = 'stock_db'
```

### 日志配置
在 `config/logConfig.py` 中配置日志输出：
```python
# 日志级别和输出格式配置
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
```

## 🔧 定时任务

系统使用APScheduler实现定时任务调度：

- **执行时间**: 每天下午6:50
- **执行条件**: 仅限交易日执行
- **任务内容**: 
  1. 题材分析
  2. 量价分析
  3. 情感分析
  4. 基本面筛选排序
  5. 技术面分析
  6. 选股策略执行

## 📊 数据输出

系统分析结果将存储在数据库中，包括：
- 股票基础信息
- 技术指标数据
- 分析评分结果
- 选股推荐列表

## 🔧 可优化点

### 性能进一步优化
1. **数据调用优化**: 目前存在大量重复的调用数据和大量无用的数据被调用，会一定程度上影响性能
2. **数据库抽象**: 数据库以及数据处理方法可以进一步抽象，并放入 `stockUtil`，方便后续的扩展和复用
3. **冗余逻辑清理**: 目前还是存在较多冗余的数据处理逻辑，需要进一步优化

### 架构改进
1. **模块化增强**: 将通用功能进一步抽象到工具类中
2. **配置管理**: 优化配置管理，支持更灵活的配置方式
3. **错误处理**: 增强错误处理和异常恢复机制

## ⚠️ 已知问题与潜在Bug

### 数据获取问题
- **空数据问题**: 在调试过程中发现，`jbm` 开头的两个文件获取很多股票数据是空的，而选股器会选出上百个股票
- **数据一致性**: 需要进一步验证数据获取的稳定性和一致性

### 业务逻辑缺陷
量价情绪题材三个维度的代码在业务逻辑上可能存在缺陷，需要进一步的测试和需求验证：

1. **排序问题**: 有的方面的分析缺乏排序逻辑
2. **数据使用不充分**: 有的方面分析获取10天以上的数据，但只用了5天
3. **时序错误**: 有的方面分析当天与第二天的数据变化，但是前后顺序有误

### 建议改进
1. **数据验证**: 增加数据完整性检查
2. **逻辑测试**: 对三个维度的分析逻辑进行全面的单元测试
3. **时序修正**: 修正数据前后顺序的处理逻辑
4. **排序完善**: 为所有分析维度添加合适的排序逻辑

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👨‍💻 作者

**Bruce Li** - [GitHub](https://github.com/your-username)

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和数据提供方。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 [Issue](https://github.com/your-username/stock-evaluation/issues)
- 发送邮件至: <EMAIL>

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
