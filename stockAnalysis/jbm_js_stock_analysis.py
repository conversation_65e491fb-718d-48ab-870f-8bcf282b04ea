from pathlib import Path
import sys
# 获取当前脚本所在目录（stockAnalysis），然后找到其父目录（your_project）
current_dir = Path(__file__).resolve().parent  # stockAnalysis 目录
root_dir = current_dir.parent                 # your_project 目录
sys.path.append(str(root_dir))
import pandas as pd
from utils.databaseUtil import create_table_and_insert_data
from utils.stockUtil import assign_stars
from utils.httpUtil import get_base_info_data, get_trade_date_data, get_day_kline_data, get_daily_market_data
from config.constant import DATABASE_GROUPS
from config.logConfig import logger

def get_all_stock_symbols():
    """
    获取全市场A股股票代码列表(排除北交所股票)
    只做数据处理，数据获取交给httpUtil
    """
    try:
        df = get_base_info_data(fields="code,name,stype")
        if not df or len(df) == 0:
            logger.info("获取股票列表失败")
            return []
        df = pd.DataFrame(df)  # get_base_info_data返回list[dict]，转DataFrame
        df['stype'] = df['stype'].astype(str).str.replace(';', '')
        stock_df = df[df['stype'] != '3']  # 去除北交所股票
        return stock_df['code'].tolist()
    except Exception as e:
        logger.error(f"获取股票列表出错: {e}")
        return []

def get_trade_date(end_date):
    """
    获取A股市场的交易日历
    只做数据处理，数据获取交给httpUtil
    """
    try:
        data = get_trade_date_data(start_date='2025-01-01', end_date=end_date, fields='mtype,tdate,isopen')
        if not data or len(data) == 0:
            logger.info("交易日历获取失败")
            return pd.DataFrame()
        df = pd.DataFrame(data)
        df['trade_date'] = pd.to_datetime(df['tdate'])
        df = df.sort_values('trade_date').set_index('trade_date')
        for col in ['mtype', 'isopen']:
            df[col] = pd.to_numeric(df[col])
        return df
    except Exception as e:
        logger.error(f"交易日数据获取失败: {str(e)}")
        return pd.DataFrame()


def calculate_sma(series, window):
    """简单移动平均"""
    return series.rolling(window=window).mean()

def calculate_ema(series, window):
    """计算指数移动平均"""
    return series.ewm(span=window, adjust=False).mean()

def calculate_macd(data):
    """计算MACD指标"""
    # 计算EMA12
    data['EMA12'] = calculate_ema(data['close'], 12)
    
    # 计算EMA26
    data['EMA26'] = calculate_ema(data['close'], 26)
    
    # 计算DIF
    data['DIF'] = data['EMA12'] - data['EMA26']
    
    # 计算DEA (MACD的9日EMA)
    data['DEA'] = calculate_ema(data['DIF'], 9)
    
    # 计算MACD柱状图
    data['MACD'] = (data['DIF'] - data['DEA']) * 2
    
    return data

def check_golden_cross(data):
    """检查MACD金叉"""
    if len(data) < 2:
        return False
    
    # 获取最后两天的数据
    last_day = data.iloc[-1]
    prev_day = data.iloc[-2]
    
    # 检查金叉条件
    golden_cross = (prev_day['DIF'] < prev_day['DEA']) and (last_day['DIF'] > last_day['DEA'])
    
    return golden_cross


def jishu_all_stocks(end_date):
    """
    扫描全市场股票(排除北交所)，找出在end_date当天有信号的股票
    :param end_date: 检查信号的日期 (格式：'20231231')
    :return: 包含所有符合条件股票的DataFrame
    """
    logger.info(f"开始执行技术分析任务，日期: {end_date}")
    
    all_symbols = get_all_stock_symbols()
    if not all_symbols:
        logger.error("无法获取股票列表，程序终止")
        return pd.DataFrame()

    logger.info(f"开始批量获取全市场{len(all_symbols)}只A股股票K线数据...")

    # 获取交易日历，确定近60日有效交易日
    trade_date_df = get_trade_date(end_date)
    if trade_date_df.empty:
        logger.error("交易日历获取失败")
        return pd.DataFrame()
    valid_dates = trade_date_df[trade_date_df['isopen'] == 3]
    if valid_dates.empty or len(valid_dates) < 60:
        logger.error("有效交易日不足60天")
        return pd.DataFrame()
    start_trade_date = valid_dates.index[-60]
    start_trade_str = pd.to_datetime(start_trade_date).strftime('%Y-%m-%d')

    # 分批获取所有股票K线数据，每次最多50个
    kline_data_list = []
    batch_size = 50
    
    for i in range(0, len(all_symbols), batch_size):
        batch_symbols = all_symbols[i:i + batch_size]
        logger.info(f"正在获取第 {i//batch_size + 1} 批股票数据，共 {len(batch_symbols)} 只股票...")
        
        batch_kline_data = get_day_kline_data(
            start_date=start_trade_str,
            end_date=end_date,
            code=','.join(batch_symbols),  # 逗号分隔字符串
            fields='code,name,tdate,open,high,low,close,cjl'
        )
        
        if batch_kline_data and len(batch_kline_data) > 0:
            kline_data_list.extend(batch_kline_data)
        else:
            logger.warning(f"第 {i//batch_size + 1} 批股票数据获取失败")
    
    if not kline_data_list:
        logger.error("所有批次K线数据获取失败")
        return pd.DataFrame()
    
    kline_df = pd.DataFrame(kline_data_list)
    kline_df['trade_date'] = pd.to_datetime(kline_df['tdate'])
    kline_df = kline_df.sort_values(['code', 'trade_date'])
    for col in ['open', 'high', 'low', 'close', 'cjl']:
        kline_df[col] = pd.to_numeric(kline_df[col], errors='coerce')

    # 按股票分组做信号判断
    results = []
    logger.info("开始分析技术指标...")
    for code, group in kline_df.groupby('code'):
        if len(group) < 26:
            continue
        group = group.set_index('trade_date')
        group['MA5_Volume'] = group['cjl'].rolling(window=5).mean()
        group['Volume_Surge_Ratio'] = group['cjl'] / group['MA5_Volume']
        # 计算MACD
        group['EMA12'] = group['close'].ewm(span=12, adjust=False).mean()
        group['EMA26'] = group['close'].ewm(span=26, adjust=False).mean()
        group['DIF'] = group['EMA12'] - group['EMA26']
        group['DEA'] = group['DIF'].ewm(span=9, adjust=False).mean()
        group['MACD'] = (group['DIF'] - group['DEA']) * 2
        # 检查金叉
        if len(group) < 2 or end_date not in group.index:
            continue
        last = group.loc[end_date]
        prev = group.iloc[-2]
        golden_cross = (prev['DIF'] < prev['DEA']) and (last['DIF'] > last['DEA'])
        if golden_cross and last['Volume_Surge_Ratio'] >= 2:
            results.append({
                'symbol': code,
                'name': last['name'],
                'signal_date': end_date,
                'close': last['close'],
                'Volume_Surge_Ratio': last['Volume_Surge_Ratio'],
                'DIF': last['DIF'],
                'DEA': last['DEA'],
                'MACD': last['MACD']
            })

    if not results:
        logger.info("扫描完成，未发现符合条件的股票")
        return pd.DataFrame()

    # 批量获取每日市场数据
    signal_codes = [item['symbol'] for item in results]
    logger.info(f"获取{len(signal_codes)}只信号股票的市场数据...")
    market_data = get_daily_market_data(
        start_date=end_date,
        end_date=end_date,
        code=','.join(signal_codes),  # 逗号分隔字符串
        fields="name,code,cje,zljlr,z50,zdfd,zded,price,lbi,z197"
    )
    market_df = pd.DataFrame(market_data) if market_data else pd.DataFrame()
    final_results = []
    for item in results:
        code = item['symbol']
        row = market_df[market_df['code'] == code].iloc[0] if not market_df.empty and code in market_df['code'].values else None
        new_item = {
            "code": code,
            "name": item['name'] if row is None else row['name'],
            "star_lev": 0,
            "star_type": "4",
            "industry": None if row is None else row.get("z197", None),
            "concept": None if row is None else row.get("z50", None),
            "insert_time": item['signal_date']
        }
        final_results.append(new_item)

    if final_results:
        logger.info("分配星级并写入数据库...")
        js_db = assign_stars(final_results)
        create_table_and_insert_data(
            data=js_db,
            table_name='ai_stock_evaluation',
            config=DATABASE_GROUPS
        )
        logger.info(f"技术分析任务执行完成，共发现{len(final_results)}只符合条件的股票")
    else:
        logger.info("扫描完成，未发现符合条件的股票")

if __name__ == "__main__":
    jishu_all_stocks("2025-07-04")
    