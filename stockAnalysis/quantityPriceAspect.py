# -*- coding: utf-8 -*-
# @Time 2025/5/17 15:34
# @Author: <PERSON>
"""
股票量价维度分析
"""

from typing import Union, Optional, cast, Any
from datetime import date
import sys
from pathlib import Path
import pandas as pd

# 获取当前脚本所在目录（stockAnalysis），然后找到其父目录（your_project）
current_dir = Path(__file__).resolve().parent  # stockAnalysis 目录
root_dir = current_dir.parent                 # your_project 目录
sys.path.append(str(root_dir))

from config.constant import URL_GROUPS, TOKEN_GROUPS, MAX_ORDER_NUMBER, DATABASE_GROUPS
from utils.commonUtil import get_recent_trading_days_array, is_trading_day
from utils.stockUtil import *
from config.logConfig import logger
from utils.databaseUtil import create_table_and_insert_data
from utils.stockUtil import assign_stars


def heavy_volume():
    """
    根据温和放量验证策略，筛选符合条件的股票集合
    要求：成交量三日连续增长环比在1.1-1.3之间，且收盘价大于五日均值
    @return: 符合条件的股票集合以及数量
    """
    # 获取股价5日均线统计
    five = get_price_average_statistic(5)

    # 获取最近4个交易日期
    trading_days = get_recent_trading_days_array(0, 4)
    today = trading_days[0].strftime("%Y-%m-%d")
    
    # 获取今日数据
    today_data = get_daily_market_data(
        start_date=today, 
        end_date=today,
        fields="code,name,price,cjl,zdfd,zded,z197,z50"
    )
    today_data = pd.DataFrame(today_data)

    # 获取前3个交易日的成交量数据
    volume_data = []
    for i in range(1, 4):
        prev_date = trading_days[i].strftime("%Y-%m-%d")
        prev_data = get_daily_market_data(
            start_date=prev_date, 
            end_date=prev_date,
            fields="code,cjl"
        )
        prev_df = pd.DataFrame(prev_data)
        prev_df = prev_df.rename(columns={"cjl": f"cjl_pre{i}"})
        volume_data.append(prev_df)

    # 合并所有数据
    heavy_statistic = today_data
    for df in volume_data:
        heavy_statistic = pd.merge(heavy_statistic, df, on="code")
    heavy_statistic = pd.merge(heavy_statistic, five, on="code")

    # 过滤无效数据：五日均线为0或价格为0的股票
    heavy_statistic = heavy_statistic[
        (heavy_statistic["price_5_average"] > 0) & 
        (heavy_statistic["price"] > 0)
    ]

    # 过滤连续成交量为零的股票
    volume_cols = ["cjl", "cjl_pre1", "cjl_pre2", "cjl_pre3"]
    heavy_statistic = heavy_statistic[
        heavy_statistic[volume_cols].gt(0).all(axis=1)  # type: ignore
    ]

    # 计算连续放量环比指标
    heavy_statistic["cjl_qoq_2"] = heavy_statistic["cjl_pre2"] / heavy_statistic["cjl_pre3"]
    heavy_statistic["cjl_qoq_1"] = heavy_statistic["cjl_pre1"] / heavy_statistic["cjl_pre2"]
    heavy_statistic["cjl_qoq"] = heavy_statistic["cjl"] / heavy_statistic["cjl_pre1"]

    # 计算均线站稳指标
    heavy_statistic["ma"] = heavy_statistic["price"] / heavy_statistic["price_5_average"]

    # 温和放量筛选：三日连续增长环比在1.1-1.3之间，且收盘价大于五日均值
    heavy_statistic = heavy_statistic[
        (heavy_statistic["cjl_qoq_2"].between(1.1, 1.3)) &  # type: ignore
        (heavy_statistic["cjl_qoq_1"].between(1.1, 1.3)) &  # type: ignore
        (heavy_statistic["cjl_qoq"].between(1.1, 1.3)) &  # type: ignore
        (heavy_statistic["ma"] > 1)
    ]

    return heavy_statistic, len(heavy_statistic)


def explosive_quantity():
    """
    根据暴量验证策略，筛选符合条件的股票集合
    条件：量能比率大于2，次日维持度大于0.7
    @return: 符合条件的股票集合以及数量
    """
    # 获取近5日成交量统计
    five = get_average_cjl_statistic(5)

    # 获取最近的两个交易日
    trading_days = get_recent_trading_days_array(0, 2)
    today = trading_days[0].strftime("%Y-%m-%d")
    previous_day = trading_days[1].strftime("%Y-%m-%d")
    
    # 获取今日数据
    today_data = get_daily_market_data(
        start_date=today, 
        end_date=today,
        fields="code,name,price,cjl,zdfd,zded,z197,z50"
    )
    today_data = pd.DataFrame(today_data)
    
    # 获取前一日数据
    previous_data = get_daily_market_data(
        start_date=previous_day, 
        end_date=previous_day,
        fields="code,cjl"
    )
    previous_data = pd.DataFrame(previous_data)
    previous_data = previous_data.rename(columns={"cjl": "previous_cjl"})

    # 合并数据
    volume_statistic = pd.merge(today_data, five, on="code")
    volume_statistic = pd.merge(volume_statistic, previous_data, on="code")

    # 过滤无效数据：成交量和五日成交量均值为0的股票
    volume_statistic = volume_statistic[
        (volume_statistic["cjl"] > 0) & 
        (volume_statistic["cjl_5_average"] > 0)
    ]

    # 计算量能比率和次日量维持度
    volume_statistic["volume_ratio"] = volume_statistic["cjl"] / volume_statistic["cjl_5_average"]
    volume_statistic["maintenance_degree"] = volume_statistic["previous_cjl"] / volume_statistic["cjl"]

    # 筛选满足暴量验证的股票：量能比率大于2，次日维持度大于0.7
    volume_statistic = volume_statistic[
        (volume_statistic["volume_ratio"] > 2) & 
        (volume_statistic["maintenance_degree"] > 0.7)
    ]

    return volume_statistic, len(volume_statistic)


def get_quantity_price_db_data(volume_statistic, vs_len, heavy_statistic, hs_len):
    """
    从符合条件的两个子集中生成最终排序结果
    优先返回同时满足条件的股票集合，否则返回并集
    @param volume_statistic: 暴量验证股票集合
    @param vs_len: 暴量验证股票数量
    @param heavy_statistic: 温和放量股票集合
    @param hs_len: 温和放量股票数量
    @return: 排序后的最终股票集合
    """
    # 处理边界情况
    if vs_len == 0 and hs_len == 0:
        return None

    if vs_len == 0:
        return heavy_statistic[:MAX_ORDER_NUMBER] if hs_len > MAX_ORDER_NUMBER else heavy_statistic

    if hs_len == 0:
        return volume_statistic[:MAX_ORDER_NUMBER] if vs_len > MAX_ORDER_NUMBER else volume_statistic

    # 将两个集合取并集
    mix_set = volume_statistic._append(heavy_statistic)

    # 获取重复的行（同时满足多个条件的股票集合）
    both_set = mix_set[mix_set.duplicated()].reset_index(drop=True)
    
    # 优先返回同时满足条件的股票集合
    if len(both_set) > 0:
        both_set = cast(pd.DataFrame, pd.DataFrame(both_set))
        both_set = both_set.sort_values(by="zdfd", ascending=False)  # type: ignore
        return both_set[:MAX_ORDER_NUMBER] if len(both_set) > MAX_ORDER_NUMBER else both_set
    else:
        # 没有交集时，从并集中选择
        mix_set = cast(pd.DataFrame, pd.DataFrame(mix_set))
        mix_set = mix_set.sort_values(by="zdfd", ascending=False).reset_index(drop=True)  # type: ignore
        return mix_set[:MAX_ORDER_NUMBER] if len(mix_set) > MAX_ORDER_NUMBER else mix_set


def create_quantity_price_order(date):
    """
    根据股票数据生成量价面排序并将结果写入数据库
    @param date: 分析日期
    @return: 处理后的数据列表
    """
    # 筛选符合暴量验证的股票
    volume_statistic, vs_len = explosive_quantity()
    volume_statistic = cast(pd.DataFrame, pd.DataFrame(volume_statistic))
    volume_statistic = volume_statistic[["code", "name", "price", "zdfd", "zded", "z197", "z50"]]
    volume_statistic = volume_statistic.sort_values(by="zdfd", ascending=False)  # type: ignore

    # 筛选符合温和放量的股票
    heavy_statistic, hs_len = heavy_volume()
    heavy_statistic = cast(pd.DataFrame, pd.DataFrame(heavy_statistic))
    heavy_statistic = heavy_statistic[["code", "name", "price", "zdfd", "zded", "z197", "z50"]]
    heavy_statistic = heavy_statistic.sort_values(by="zdfd", ascending=False)  # type: ignore

    # 获取排序后的数据
    insert_data = get_quantity_price_db_data(volume_statistic, vs_len, heavy_statistic, hs_len)
    data_group = []
    
    # 处理插入数据
    if insert_data is not None and len(insert_data) > 0:
        if not isinstance(insert_data, pd.DataFrame):
            insert_data = pd.DataFrame(insert_data)
            
        for _, row in insert_data.iterrows():
            row_dict = row.to_dict()
            insert_da = {
                "code": row_dict["code"],
                "name": row_dict["name"],
                "star_lev": 0,
                "star_type": "1",
                "industry": row_dict["z197"],
                "concept": row_dict["z50"],
                "insert_time": date
            }
            data_group.append(insert_da)
        logger.debug(f"记录生成的插入数据：{insert_data.head(2)}")
    else:
        logger.warning("没有符合条件的股票数据")
        
    return data_group


def quantity_price_analysis_task(date):
    """
    股票量价面分析任务主函数
    @param date: 分析日期
    """
    qu_db = create_quantity_price_order(date)
    qu_db = assign_stars(qu_db)

    create_table_and_insert_data(
        data=qu_db,
        table_name='ai_stock_evaluation',
        config=DATABASE_GROUPS
    )

if __name__ == "__main__":
    quantity_price_analysis_task("2025-05-23")
