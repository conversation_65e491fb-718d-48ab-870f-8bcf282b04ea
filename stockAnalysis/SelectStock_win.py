import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys

# 获取当前脚本所在目录（stockAnalysis），然后找到其父目录
current_dir = Path(__file__).resolve().parent  # stockAnalysis 目录
root_dir = current_dir.parent                 # 父目录
sys.path.append(str(root_dir))

from utils.httpUtil import get_base_info_data, get_trade_date_data, get_day_kline_data
from utils.databaseUtil import create_table_if_not_exist, insert_into_table
from config.logConfig import logger
from config.constant import DATABASE_GROUPS

# 全局配置
TOP_N = 20  # 重点关注前N只股票
VOLUME_RATIO_THRESHOLD = 1.2  # 成交量放大阈值(1.2表示比5日均量高20%)
MIN_STRICT_MATCH = 5  # 同时满足两个策略的最小股票数量，不足则放宽条件

# 全局缓存变量
PREV_1_TRADING_DAY = None  # 前1个交易日
PREV_5_TRADING_DAY = None  # 前5个交易日

def initialize_trading_day_cache(end_date):
    """初始化交易日缓存"""
    global PREV_1_TRADING_DAY, PREV_5_TRADING_DAY
    
    logger.info("正在初始化交易日缓存...")
    
    # 获取前1个交易日
    PREV_1_TRADING_DAY = get_nth_previous_trading_day(1, end_date)
    if PREV_1_TRADING_DAY:
        logger.info(f"前1个交易日: {PREV_1_TRADING_DAY}")
    else:
        logger.error("获取前1个交易日失败")
    
    # 获取前5个交易日
    PREV_5_TRADING_DAY = get_nth_previous_trading_day(5, end_date)
    if PREV_5_TRADING_DAY:
        logger.info(f"前5个交易日: {PREV_5_TRADING_DAY}")
    else:
        logger.error("获取前5个交易日失败")

def get_all_stock_symbols():
    """获取全市场A股股票代码列表(排除北交所股票)"""
    try:
        df = get_base_info_data(fields="code,name,stype")
        if not df or len(df) == 0:
            logger.info("获取股票列表失败")
            return []
        df = pd.DataFrame(df)  # get_base_info_data返回list[dict]，转DataFrame
        df['stype'] = df['stype'].astype(str).str.replace(';', '')
        stock_df = df[df['stype'] != '3']  # 去除北交所股票
        return stock_df['code'].tolist()
    except Exception as e:
        logger.error(f"获取股票列表出错: {e}")
        return []

def get_trade_date(end_date):
    """获取A股市场的交易日历"""
    try:
        data = get_trade_date_data(start_date='2025-01-01', end_date=end_date, fields='mtype,tdate,isopen')
        if not data or len(data) == 0:
            logger.info("交易日历获取失败")
            return pd.DataFrame()
        df = pd.DataFrame(data)
        df['trade_date'] = pd.to_datetime(df['tdate'])
        df = df.sort_values('trade_date').set_index('trade_date')
        for col in ['mtype', 'isopen']:
            df[col] = pd.to_numeric(df[col])
        return df
    except Exception as e:
        logger.error(f"交易日数据获取失败: {str(e)}")
        return pd.DataFrame()

def get_nth_previous_trading_day(n, end_date):
    """往前获取第n个交易日的日期"""
    try:
        end_date_dt = pd.to_datetime(end_date)
        data = get_trade_date_data(start_date="2025-01-01", end_date=end_date_dt.strftime('%Y-%m-%d'), fields='mtype,tdate,isopen,mkt')
        if not data or len(data) == 0:
            logger.info("交易日历获取失败")
            return None
        
        df = pd.DataFrame(data)
        # 过滤出交易日
        trading_days = df[df['isopen'] == 3]['tdate'].tolist()

        # 将交易日转换为datetime对象
        trading_days = [datetime.strptime(date, "%Y-%m-%d") for date in trading_days]

        # 获取当前日期前n个交易日
        previous_n_trading_days = [date for date in trading_days if date <= end_date_dt][-n-1:]

        # 检查是否有足够的交易日
        if len(previous_n_trading_days) < n:
            return None

        # 获取第n个交易日并转换回字符串格式
        nth_previous_trading_day = previous_n_trading_days[0].strftime("%Y-%m-%d")

        return nth_previous_trading_day
    except Exception as e:
        logger.error(f"获取第{n}个交易日失败: {str(e)}")
        return None

def calculate_win_rate(df, end_date):
    """计算股票赢率（近30个交易日上涨天数占比）"""
    if df.empty:
        return None

    # 取最近30个交易日的数据
    recent_30_days = df.tail(30)

    # 计算上涨天数
    up_days = recent_30_days[recent_30_days['zdf'] > 0].shape[0]

    # 计算赢率
    win_rate = up_days / 30
    return win_rate

# ========== 六脉神剑策略 ==========
def calculate_liumaijian_indicators(df):
    """计算六脉神剑技术指标"""
    # 基础加权价格
    df['HHJSJDA'] = (3 * df['close'] + df['open'] + df['low'] + df['high']) / 6

    # 黄线计算（20日递减权重）
    weights = np.arange(20, 0, -1)

    def weighted_avg(series):
        return np.dot(series.values[-20:], weights) / 210  # 210 = sum(1-20)

    df['yellow_line'] = df['HHJSJDA'].rolling(20).apply(weighted_avg, raw=False)

    # 绿线计算（5日SMA）
    df['green_line'] = df['yellow_line'].rolling(5).mean()

    return df

def generate_liumaijian_signal(df, end_date):
    """生成六脉神剑交易信号"""
    if df.empty:
        return False

    # 计算指标
    df = calculate_liumaijian_indicators(df)

    # 确保end_date在数据中
    end_date_dt = pd.to_datetime(end_date)
    if end_date_dt not in df.index:
        return False

    # 信号条件 - 使用缓存的交易日
    global PREV_1_TRADING_DAY
    if not PREV_1_TRADING_DAY:
        return False
    
    prev_day = pd.to_datetime(PREV_1_TRADING_DAY)
    if prev_day not in df.index:
        return False

    prev_yellow = df.loc[prev_day, 'yellow_line']
    prev_green = df.loc[prev_day, 'green_line']
    curr_yellow = df.loc[end_date_dt, 'yellow_line']
    curr_green = df.loc[end_date_dt, 'green_line']

    signal = (prev_yellow < prev_green) & (curr_yellow > curr_green)

    return bool(signal)

# ========== 短线趋势策略 ==========
def calculate_ema(series, window):
    """指数移动平均计算"""
    return series.ewm(span=window, adjust=False).mean()

def calculate_sma(series, window):
    """简单移动平均"""
    return series.rolling(window=window).mean()

def calculate_duanxianqushi_indicators(df):
    """计算短线趋势策略指标"""
    if df.empty:
        return df

    # MACD 指标
    df['EMA8'] = calculate_ema(df['close'], 8)
    df['EMA13'] = calculate_ema(df['close'], 13)
    df['DIFF'] = df['EMA8'] - df['EMA13']
    df['DEA'] = calculate_ema(df['DIFF'], 5)
    df['MACD_Signal'] = (df['DIFF'] > df['DEA']).astype(int)

    # KDJ 指标
    low_min = df['low'].rolling(8).min()
    high_max = df['high'].rolling(8).max()
    df['RSV'] = (df['close'] - low_min) / (high_max - low_min + 1e-6) * 100  # 避免除以0
    df['K'] = calculate_ema(df['RSV'], 3)
    df['D'] = calculate_ema(df['K'], 3)
    df['KDJ_Signal'] = (df['K'] > df['D']).astype(int)

    # RSI 指标
    delta = df['close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    df['RSI5'] = calculate_ema(gain, 5) / (calculate_ema(loss, 5) + 1e-6) * 100
    df['RSI13'] = calculate_ema(gain, 13) / (calculate_ema(loss, 13) + 1e-6) * 100
    df['RSI_Signal'] = (df['RSI5'] > df['RSI13']).astype(int)

    # LWR 指标
    hh = df['high'].rolling(13).max()
    ll = df['low'].rolling(13).min()
    df['ZZSC'] = -(hh - df['close']) / (hh - ll + 1e-6) * 100
    df['LWR1'] = calculate_ema(df['ZZSC'], 3)
    df['LWR2'] = calculate_ema(df['LWR1'], 3)
    df['LWR_Signal'] = (df['LWR1'] > df['LWR2']).astype(int)

    # BBI 指标
    ma3 = calculate_sma(df['close'], 3)
    ma5 = calculate_sma(df['close'], 5)
    ma8 = calculate_sma(df['close'], 8)
    ma13 = calculate_sma(df['close'], 13)
    df['BBI'] = (ma3 + ma5 + ma8 + ma13) / 4
    df['BBI_Signal'] = (df['close'] > df['BBI']).astype(int)

    # ZLMM 指标
    mtm = df['close'].diff()
    df['MMS'] = calculate_ema(calculate_ema(mtm, 5), 3) / \
                (calculate_ema(calculate_ema(mtm.abs(), 5), 3) + 1e-6) * 100
    df['MMM'] = calculate_ema(calculate_ema(mtm, 13), 8) / \
                (calculate_ema(calculate_ema(mtm.abs(), 13), 8) + 1e-6) * 100
    df['ZLMM_Signal'] = (df['MMS'] > df['MMM']).astype(int)

    return df

def generate_duanxianqushi_signal(df, end_date):
    """生成短线趋势交易信号"""
    if df.empty:
        return False

    # 计算指标
    df = calculate_duanxianqushi_indicators(df)

    # 确保end_date在数据中
    end_date_dt = pd.to_datetime(end_date)
    if end_date_dt not in df.index:
        return False

    # 所有需要满足的条件
    conditions = [
        'MACD_Signal', 'KDJ_Signal',
        'RSI_Signal', 'LWR_Signal',
        'BBI_Signal', 'ZLMM_Signal'
    ]

    # 检查当天是否满足所有条件
    today_conditions = df.loc[end_date, conditions]
    all_conditions_met = today_conditions.sum() == len(conditions)

    # 检查前一天是否不满足所有条件 - 使用缓存的交易日
    global PREV_1_TRADING_DAY
    if not PREV_1_TRADING_DAY:
        prev_not_met = True
    else:
        prev_day = pd.to_datetime(PREV_1_TRADING_DAY)
        if prev_day in df.index:
            prev_conditions = df.loc[prev_day, conditions]
            prev_not_met = prev_conditions.sum() < len(conditions)
        else:
            prev_not_met = True

    # 买入信号：当日满足且前一日不满足
    return bool(all_conditions_met and prev_not_met)

def calculate_volume_features(df, end_date):
    """计算成交量相关特征"""
    end_date_dt = pd.to_datetime(end_date)
    if end_date_dt not in df.index:
        return 0, 0

    # 计算5日成交量均值 - 使用缓存的交易日
    global PREV_5_TRADING_DAY
    if not PREV_5_TRADING_DAY:
        return 0, 0
    
    df_last_5 = df.loc[PREV_5_TRADING_DAY:end_date_dt.strftime('%Y-%m-%d')]
    if len(df_last_5) < 2:  # 至少需要2天数据
        return 0, 0

    avg_volume_5 = df_last_5['cjl'].iloc[:-1].mean()  # 排除当天计算5日均量

    # 当日成交量
    today_volume = df.loc[end_date_dt, 'cjl']

    # 计算成交量比率
    volume_ratio = today_volume / (avg_volume_5 + 1e-6)  # 避免除以0

    return today_volume, volume_ratio

def process_stock_data(kline_df, end_date):
    """处理股票数据，返回信号信息"""
    results = []
    
    for code, group in kline_df.groupby('code'):
        if len(group) < 50:  # 至少需要50天数据
            continue
            
        group = group.set_index('trade_date')
        group = group.sort_index()
        
        # 确保end_date在数据中
        end_date_dt = pd.to_datetime(end_date)
        if end_date_dt not in group.index:
            continue

        # 计算涨跌幅 - 使用缓存的交易日
        global PREV_1_TRADING_DAY
        if not PREV_1_TRADING_DAY:
            pct_change = 0
        else:
            prev_day = pd.to_datetime(PREV_1_TRADING_DAY)
            if prev_day in group.index:
                prev_close = group.loc[prev_day, 'close']
                current_close = group.loc[end_date_dt, 'close']
                pct_change = (current_close - prev_close) / prev_close * 100
            else:
                pct_change = 0

        # 计算成交量特征
        volume, volume_ratio = calculate_volume_features(group, end_date)

        # 检查两个策略的信号
        liumaijian_signal = generate_liumaijian_signal(group.copy(), end_date)
        duanxianqushi_signal = generate_duanxianqushi_signal(group.copy(), end_date)

        # 如果两个策略都有信号
        if liumaijian_signal and duanxianqushi_signal:
            signal_type = "both"
        elif liumaijian_signal:
            signal_type = "liumaijian"
        elif duanxianqushi_signal:
            signal_type = "duanxianqushi"
        else:
            continue

        # 计算赢率
        win_rate = calculate_win_rate(group, end_date)

        results.append({
            'symbol': code,
            'name': group.loc[end_date_dt, 'name'],
            'signal_date': end_date,
            'close': group.loc[end_date_dt, 'close'],
            'pct_change': pct_change,
            'volume': volume,
            'volume_ratio': volume_ratio,
            'is_volume_up': volume_ratio >= VOLUME_RATIO_THRESHOLD,
            'signal_type': signal_type,
            'win_rate': win_rate if win_rate is not None else 0
        })
    
    return results

def create_selected_stocks_win_table():
    """创建selected_stocks_win表"""
    columns = [
        "id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增整数'",
        "symbol VARCHAR(10) NOT NULL COMMENT '股票代码'",
        "name VARCHAR(100) NOT NULL COMMENT '股票名称'",
        "pct_change DECIMAL(10, 2) NOT NULL COMMENT '涨跌幅，保留两位小数'",
        "volume DECIMAL(15, 2) NOT NULL COMMENT '成交量，保留两位小数'",
        "volume_ratio DECIMAL(10, 2) NOT NULL COMMENT '成交量放大倍数，保留两位小数'",
        "signal_type VARCHAR(50) NOT NULL COMMENT '信号类型，表示股票满足的策略类型'",
        "win_rate DECIMAL(10, 2) NOT NULL COMMENT '赢率，保留两位小数'",
        "insert_date DATE NOT NULL COMMENT '数据插入日期'",
        "insert_time DATETIME NOT NULL COMMENT '数据插入时间'",
        "remarks TEXT COMMENT '备注信息'",
        "INDEX idx_insert_date (insert_date) COMMENT '索引，用于提高按插入日期查询的效率'"
    ]
    
    create_table_if_not_exist(DATABASE_GROUPS, 'selected_stocks_win', columns)

def insert_data_to_db(final_results, end_date):
    """将筛选结果插入到数据库"""
    try:
        # 创建表
        create_selected_stocks_win_table()
        
        # 将end_date转换为DATE格式
        insert_date = pd.to_datetime(end_date).date()
        
        # 清空当天数据
        from utils.databaseUtil import delete_update_table
        clear_sql = "DELETE FROM selected_stocks_win WHERE insert_date = %s"
        delete_update_table(DATABASE_GROUPS, clear_sql, [(insert_date,)], 'selected_stocks_win')
        logger.info(f"已清空insert_date为{insert_date}的数据")

        # 准备插入数据
        data_to_insert = []
        for _, row in final_results.iterrows():
            remarks = f"满足条件: {row['signal_type']}"
            data_to_insert.append({
                'symbol': row['symbol'],
                'name': row['name'],
                'pct_change': row['pct_change'],
                'volume': row['volume'],
                'volume_ratio': row['volume_ratio'],
                'signal_type': row['signal_type'],
                'win_rate': row['win_rate'],
                'insert_date': insert_date,
                'insert_time': datetime.now(),
                'remarks': remarks
            })

        # 插入数据
        columns = ['symbol', 'name', 'pct_change', 'volume', 'volume_ratio', 'signal_type', 'win_rate', 'insert_date', 'insert_time', 'remarks']
        insert_into_table(DATABASE_GROUPS, data_to_insert[:5], 'selected_stocks_win', columns)
        logger.info(f"成功插入{len(data_to_insert[:5])}条记录到selected_stocks_win表中")
        
    except Exception as e:
        logger.error(f"数据库操作出错: {e}")

def select_stocks_win(end_date):
    """
    扫描全市场股票，按照以下逻辑筛选：
    1. 首先筛选同时满足两个策略条件的股票
    2. 如果数量不足MIN_STRICT_MATCH只，则放宽条件筛选满足任一策略条件的股票
    3. 按成交量放大程度和涨幅综合排序
    """
    # 初始化交易日缓存
    initialize_trading_day_cache(end_date)
    
    all_symbols = get_all_stock_symbols()
    if not all_symbols:
        logger.error("无法获取股票列表，程序终止")
        return pd.DataFrame()

    logger.info(f"开始扫描全市场{len(all_symbols)}只A股股票(已排除北交所股票)...")
    logger.info(f"检查日期: {end_date}")

    # 获取交易日历，确定近50日有效交易日
    trade_date_df = get_trade_date(end_date)
    if trade_date_df.empty:
        logger.error("交易日历获取失败")
        return pd.DataFrame()
    valid_dates = trade_date_df[trade_date_df['isopen'] == 3]
    if valid_dates.empty or len(valid_dates) < 50:
        logger.error("有效交易日不足50天")
        return pd.DataFrame()
    start_trade_date = valid_dates.index[-50]
    start_trade_str = pd.to_datetime(start_trade_date).strftime('%Y-%m-%d')

    # 分批获取所有股票K线数据，每次最多50个
    kline_data_list = []
    batch_size = 50
    
    for i in range(0, len(all_symbols), batch_size):
        batch_symbols = all_symbols[i:i + batch_size]
        logger.info(f"正在获取第 {i//batch_size + 1} 批股票数据，共 {len(batch_symbols)} 只股票...")
        
        batch_kline_data = get_day_kline_data(
            start_date=start_trade_str,
            end_date=end_date,
            code=','.join(batch_symbols),  # 逗号分隔字符串
            fields='code,name,tdate,open,high,low,close,cjl,zdf'
        )
        
        if batch_kline_data and len(batch_kline_data) > 0:
            kline_data_list.extend(batch_kline_data)
        else:
            logger.warning(f"第 {i//batch_size + 1} 批股票数据获取失败")
    
    if not kline_data_list:
        logger.error("所有批次K线数据获取失败")
        return pd.DataFrame()
    
    kline_df = pd.DataFrame(kline_data_list)
    kline_df['trade_date'] = pd.to_datetime(kline_df['tdate'])
    kline_df = kline_df.sort_values(['code', 'trade_date'])
    for col in ['open', 'high', 'low', 'close', 'cjl', 'zdf']:
        kline_df[col] = pd.to_numeric(kline_df[col], errors='coerce')

    # 处理股票数据
    logger.info("开始分析技术指标...")
    results = process_stock_data(kline_df, end_date)

    if not results:
        logger.info("\n扫描完成，未发现符合条件的股票")
        return pd.DataFrame()

    # 转换为DataFrame
    result_df = pd.DataFrame(results)

    # 首先筛选同时满足两个策略的股票
    strict_results = result_df[result_df['signal_type'] == "both"]

    # 判断是否放宽条件
    if len(strict_results) >= MIN_STRICT_MATCH:
        logger.info(f"\n发现{len(strict_results)}只同时满足两个策略的股票(≥{MIN_STRICT_MATCH}只)，使用严格条件")
        final_results = strict_results
    else:
        logger.info(
            f"\n仅发现{len(strict_results)}只同时满足两个策略的股票(<{MIN_STRICT_MATCH}只)，放宽条件筛选满足任一策略的股票")
        relaxed_results = result_df[result_df['signal_type'].isin(["liumaijian", "duanxianqushi"])]
        logger.info(f"发现{len(relaxed_results)}只满足任一策略的股票")
        final_results = relaxed_results

    # 按成交量放大程度和涨幅综合排序
    # 优先选择放量的股票(volume_ratio >= 1.2)，然后按涨幅排序
    final_results = final_results.sort_values(  # type: ignore
        by=['is_volume_up', 'volume_ratio', 'pct_change'],
        ascending=[False, False, False]
    )

    # 标记前20只重点关注
    final_results['focus'] = False
    final_results.iloc[:TOP_N, final_results.columns.get_loc('focus')] = True

    # 打印重点关注股票
    logger.info(f"\n重点关注股票(成交量放大{VOLUME_RATIO_THRESHOLD}倍以上且涨幅靠前的{TOP_N}名):")
    logger.info(final_results[final_results['focus']][
              ['symbol', 'name', 'pct_change', 'volume', 'volume_ratio', 'signal_type','win_rate']
          ])

    # 将结果插入到数据库
    insert_data_to_db(final_results, end_date)
    
    return final_results

if __name__ == "__main__":
    end_date = "2025-07-04"
    select_stocks_win(end_date)
    