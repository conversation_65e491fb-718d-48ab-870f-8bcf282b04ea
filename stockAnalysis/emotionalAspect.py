# -*- coding: utf-8 -*-
# @Time 2025/5/16 21:20
# @Author: <PERSON>
"""
股票情绪维度分析模块

该模块主要功能：
1. 分析股市跌停和涨停情况
2. 计算市场环境变量（牛市/熊市/震荡市）
3. 处理股票数据并计算情绪指标
4. 生成情绪面排序并写入数据库
"""
from datetime import date
import sys
from pathlib import Path

# 获取当前脚本所在目录（stockAnalysis），然后找到其父目录（your_project）
current_dir = Path(__file__).resolve().parent  # stockAnalysis 目录
root_dir = current_dir.parent                 # your_project 目录
sys.path.append(str(root_dir))

import pandas as pd

# 导入配置相关模块
from config.constant import STOCK_GROUPS, DATABASE_GROUPS
from config.logConfig import logger

# 导入工具模块
from utils.commonUtil import get_recent_trading_days
from utils.httpUtil import get_dtgc_data, get_ztgc_data, get_daily_market_data
from utils.stockUtil import (
    create_daily_stock_count_report, 
    get_sentiment_index_statistic, 
    calculate_sentiment_index_by_statistic_data,
    assign_stars
)
from utils.databaseUtil import create_table_and_insert_data


def get_bearish_and_bullish_statistic(interval=0, days=1, situation="DOWN"):
    """
    分析股市N天内的跌停或者涨停情况

    参数:
    - interval: 间隔天数，默认为0（当天）
    - days: 向前回溯的天数，默认为1
    - situation: 分析跌停或者涨停，"DOWN"表示跌停，"UP"表示涨停，默认为"DOWN"

    返回:
    - DataFrame: 经统计后的跌停或涨停数据，包含每日股票数量统计
    """
    # 获取近期交易日日期范围
    start_date, end_date = get_recent_trading_days(interval, days)
    
    if situation == "DOWN":
        logger.info("开始股票跌停统计分析")
        logger.info(f"获取跌停数据: {start_date} 到 {end_date}")
        res_1 = get_dtgc_data(start_date, end_date, fields="all")
        result = pd.DataFrame(res_1)
    elif situation == "UP":
        logger.info("开始股票涨停统计分析")
        logger.info(f"获取涨停数据: {start_date} 到 {end_date}")
        res_2 = get_ztgc_data(start_date, end_date, fields="all")
        result = pd.DataFrame(res_2)
    else:
        logger.warning("参数situation必须为DOWN或者UP之一")
        return None

    if result is not None and not result.empty:
        # 数据预处理：转换日期格式并排序
        result['tdate'] = pd.to_datetime(result['tdate'])
        result = result.sort_values('tdate')

        # 去除重复数据
        result = result.drop_duplicates()

        # 创建并导出每日跌停股票数量统计
        daily_stock_count = create_daily_stock_count_report(result, situation)
        if daily_stock_count is None:
            if situation == "DOWN":
                logger.warning("每日股票跌停统计为空，请检查")
            elif situation == "UP":
                logger.warning("每日股票涨停统计为空，请检查")
            else:
                logger.warning("参数situation必须为DOWN或者UP之一")
        else:
            return daily_stock_count
    else:
        logger.warning("未获取到有效跌停或者涨停数据")
        return None


def get_market_environment(dt_df, zt_df):
    """
    计算市场环境变量，根据涨停和跌停数量判断市场状态
    
    参数:
    - dt_df: 跌停统计数据DataFrame
    - zt_df: 涨停统计数据DataFrame
    
    返回:
    - tuple: (市场环境描述, 市场环境数值, 最新交易日期)
        - 市场环境描述: "牛市"/"熊市"/"震荡市"/"未能确定市场环境"
        - 市场环境数值: 1(牛市)/0(震荡市)/-1(熊市)/0(未确定)
        - 最新交易日期: 用于判断的日期
    """
    logger.info("\n========== 市场环境变量计算 ==========")

    # 解析日期格式
    dt_df['交易日期'] = pd.to_datetime(dt_df['交易日期'])
    zt_df['交易日期'] = pd.to_datetime(zt_df['交易日期'])

    # 获取当前日期作为参考
    today = pd.Timestamp(date.today())

    # 找出两个集合中共有的日期
    # TODO: 这里寻找最近交易日期的逻辑存在问题，后面需要修改
    common_dates = set(dt_df['交易日期']).intersection(set(zt_df['交易日期']))

    if not common_dates:
        logger.error("两个文件中没有找到共同的交易日期")
        return "未能确定市场环境", 0, None

    # 找出距离当前日期最近的交易日
    latest_date = min(common_dates, key=lambda x: abs(x - today))
    # TODO: 这里寻找最近交易日期的逻辑存在问题，后面需要修改

    # 获取该日期对应的涨停和跌停数据
    dt_count = dt_df[dt_df['交易日期'] == latest_date]['跌停股票数量'].values[0]
    zt_count = zt_df[zt_df['交易日期'] == latest_date]['涨停股票数量'].values[0]

    # 根据条件判断市场环境
    market_env = "未能确定市场环境"
    market_env_value = 0  # 默认为震荡市或未确定状态

    # 显示判断过程
    logger.info("\n市场环境判断条件检查:")
    logger.info(
        f"条件1: 涨停数量({zt_count}) >= 80 且 跌停数量({dt_count}) <= 20? {'是' if zt_count >= 80 and dt_count <= 20 else '否'}")
    logger.info(
        f"条件2: 涨停数量({zt_count}) >= 50 且 跌停数量({dt_count}) <= 50? {'是' if zt_count >= 50 and dt_count <= 50 else '否'}")
    logger.info(
        f"条件3: 涨停数量({zt_count}) >= 30 且 跌停数量({dt_count}) <= 10? {'是' if zt_count >= 30 and dt_count <= 10 else '否'}")

    # 市场环境判断逻辑
    if zt_count >= 80 and dt_count <= 20:
        market_env = "牛市"
        market_env_value = 1
    elif zt_count >= 50 and dt_count <= 50:
        market_env = "震荡市"
        market_env_value = 0
    elif zt_count >= 30 and dt_count <= 10:
        market_env = "熊市"
        market_env_value = -1

    logger.info(f"市场环境判断结果: {market_env} (数值: {market_env_value})")
    logger.info("========== 市场环境变量计算结束 ==========\n")

    return market_env, market_env_value, latest_date


def process_stock_data(date, days=30):
    """
    处理股票数据并获得情绪排序结果
    
    参数:
    - date: 处理日期
    - days: 统计天数，默认为30个交易日
    
    返回:
    - DataFrame: 包含情绪指标的所有股票数据
    """
    logger.info("\n========== 开始处理股票数据 ==========")

    # 首先获取市场环境变量，根据近30交易日情况进行判断
    dt_df = get_bearish_and_bullish_statistic(0, days, STOCK_GROUPS["STOCK_SITUATION_DOWN"])
    zt_df = get_bearish_and_bullish_statistic(0, days, STOCK_GROUPS["STOCK_SITUATION_UP"])
    market_env, market_env_value, market_date = get_market_environment(dt_df, zt_df)

    # 获取当前日期作为参考
    today = date

    # 收集一定时间内的所有股票数据，统计指标的最大值和最小值，默认为30个交易日的数据
    index_statistic = get_sentiment_index_statistic(days)

    logger.info("\n开始处理每个股票文件并计算情绪指标...\n")

    # 获取当天的市场数据
    daily_data = get_daily_market_data(
        start_date=today, 
        end_date=today, 
        fields="code,name,tdate,price,cje,cjl,hslv,zrspj,zljlr,zdfd,zded,z197,z50"
    )
    daily_data = pd.DataFrame(daily_data)
    
    # 根据统计数据和市场环境计算情绪指标
    all_stocks_data = calculate_sentiment_index_by_statistic_data(daily_data, index_statistic, market_env_value)
    all_stocks_data = pd.DataFrame(all_stocks_data)

    logger.info("\n========== 股票数据处理结束 ==========")

    return all_stocks_data


def create_sentiment_order(date):
    """
    根据股票数据生成情绪面排序并将结果写入数据库
    
    参数:
    - date: 处理日期
    
    返回:
    - list: 包含股票情绪排序数据的字典列表
    """
    # 默认使用30天内的股票数据进行排序
    all_stocks_data = process_stock_data(date=date)
    
    # 构建写入数据库的数据结构
    data_group = []
    
    for i, r in all_stocks_data.iterrows():
        r = r.to_dict()
        insert_da = {
            "code": r["code"],
            "name": r["name"],
            "star_lev": 0,  # 星级等级，初始为0
            "star_type": "2",  # 星级类型
            "industry": r["z197"],  # 行业信息
            "concept": r["z50"],    # 概念信息
            "insert_time": date
        }
        data_group.append(insert_da)
    
    logger.debug(f"记录生成的插入数据：{data_group[:2]}")
    return data_group


def emotional_analysis_task(date_str):
    """
    股票情绪面分析任务主函数
    
    参数:
    - date_str: 分析日期字符串，格式为"YYYY-MM-DD"
    
    功能:
    1. 生成情绪排序数据
    2. 分配星级
    3. 将结果写入数据库
    """
    logger.info(f"开始执行股票情绪面分析任务，日期: {date_str}")
    
    # 生成情绪排序数据
    em_db = create_sentiment_order(date_str)
    
    # 分配星级
    em_db = assign_stars(em_db)
    
    # 将结果写入数据库
    create_table_and_insert_data(
        data=em_db,
        table_name='ai_stock_evaluation',
        config=DATABASE_GROUPS
    )
    
    logger.info("股票情绪面分析任务执行完成")


if __name__ == "__main__":
    # 测试运行
    emotional_analysis_task("2025-05-23")
