import requests
import os
import glob
import pandas as pd
import sys
from pathlib import Path

# 获取当前脚本所在目录（stockAnalysis），然后找到其父目录（your_project）
current_dir = Path(__file__).resolve().parent  # stockAnalysis 目录
root_dir = current_dir.parent                 # your_project 目录
sys.path.append(str(root_dir))
from config.logConfig import logger
from tqdm import tqdm
from pymysql import Error
from utils.databaseUtil import create_table_and_insert_data
from utils.stockUtil import assign_stars
from config.constant import DATABASE_GROUPS
from utils.httpUtil import get_daily_market_data

# 配置日志
# logger = logging.getLogger(__name__)


def ticai_task(date):
    """
    行业题材资金流向分析任务
    
    参数:
    - date: 分析日期，格式为"YYYY-MM-DD"
    
    功能:
    1. 获取指定日期的市场数据
    2. 按行业题材分组统计主力净流入
    3. 对行业分级并筛选龙头股
    4. 分配星级并写入数据库
    """
    logger.info(f"开始执行行业题材分析任务，日期: {date}")
    start_date = date
    end_date = date
    # 获取市场数据
    logger.info("获取市场数据...")
    market_data = get_daily_market_data(start_date, end_date,fields="name,code,cje,zljlr,z50,zdfd,zded,price,lbi,z197")
    groups = {}
    logger.info("按行业题材分组统计主力净流入...")
    for item in market_data:
        z50 = item["z50"]
        zljlr = item["zljlr"]
        if z50 not in groups:
            groups[z50] = {
                "sum_zljlr": zljlr,
                "items": [item]
            }
        else:
            groups[z50]["sum_zljlr"] += zljlr
            groups[z50]["items"].append(item)
    result = [
        {
            "z50": z50,
            "sum_zljlr": info["sum_zljlr"],
            "items": info["items"]
        }
        for z50, info in groups.items()
    ]
    # 行业分级
    s_hangye = []
    a_hangye = []
    b_hangye = []
    logger.info("对行业题材进行分级...")
    for hangye in result:
        if hangye["sum_zljlr"]>=2000000000:
            s_hangye.append(hangye)
        elif hangye["sum_zljlr"]>=1000000000 and hangye["sum_zljlr"]<2000000000:
            a_hangye.append(hangye)
        elif hangye["sum_zljlr"]>=500000000 and hangye["sum_zljlr"]<1000000000:
            b_hangye.append(hangye)
    # 筛选龙头股
    logger.info("筛选各级行业中的龙头股...")
    s_gupiao=[gupiao for item in s_hangye for gupiao in item["items"] if gupiao["lbi"]>=3]
    a_gupiao=[gupiao for item in a_hangye for gupiao in item["items"] if gupiao["lbi"]>=3]
    b_gupiao=[gupiao for item in b_hangye for gupiao in item["items"] if gupiao["lbi"]>=3]
    gupiao = s_gupiao+a_gupiao+b_gupiao
    logger.info(f"共筛选出{len(gupiao)}只龙头股")
    # 按成交额排序
    sorted_gupiao = sorted(gupiao, reverse=True,key=lambda x: x["cje"])
    gupiao_db = []
    logger.info("构建数据库插入数据...")
    for index, item in enumerate(sorted_gupiao, start=1):
        # 创建新字典避免修改原数据（可选）
        new_item = {
            "code": item["code"],
            "name": item["name"],
            "star_lev": 0,
            "star_type": "3",
            "industry":item["z197"],
            "concept":item["z50"],
            "insert_time":start_date
        }
        gupiao_db.append(new_item)
    # 分配星级
    logger.info("分配星级...")
    tc_db = assign_stars(gupiao_db)
    # 写入数据库
    logger.info("写入数据库...")
    create_table_and_insert_data(
        data=tc_db,
        table_name='ai_stock_evaluation',
        config = DATABASE_GROUPS
    )
    logger.info("行业题材分析任务执行完成")

# 使用示例
if __name__ == "__main__":
    ticai_task("2025-05-23")