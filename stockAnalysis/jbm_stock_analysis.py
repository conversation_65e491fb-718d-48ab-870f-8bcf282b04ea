# 导入必要的库
import pandas as pd
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 获取当前脚本所在目录（stockAnalysis），然后找到其父目录（your_project）
current_dir = Path(__file__).resolve().parent  # stockAnalysis 目录
root_dir = current_dir.parent                 # your_project 目录
sys.path.append(str(root_dir))

from config.logConfig import logger
from utils.databaseUtil import create_table_and_insert_data
from utils.stockUtil import assign_stars
from config.constant import DATABASE_GROUPS
from utils.httpUtil import get_base_info_data, get_daily_market_data, get_nb_report_data




def get_all_stock_symbols():
    """
    获取全市场A股股票代码列表(排除北交所股票)
    只做数据处理，数据获取交给httpUtil
    """
    try:
        logger.info("获取全市场股票代码列表...")
        df = get_base_info_data(fields="code,name,stype")
        if not df or len(df) == 0:
            logger.warning("获取股票代码列表失败: 无数据返回")
            return []
        df = pd.DataFrame(df)  # get_base_info_data返回list[dict]，转DataFrame
        df['stype'] = df['stype'].astype(str).str.replace(';', '')
        stock_df = df[df['stype'] != '3']  # 去除北交所股票
        logger.info(f"成功获取{len(stock_df)}只股票代码")
        return stock_df['code'].tolist()
    except Exception as e:
        logger.error(f"获取股票代码列表出错: {e}")
        return []

# 获取全市场股票的ROE:roe,资产负债率:fzl,市盈率:dsyl,行业板块:z50
def get_stock_fundamental1_symbols(start_date, end_date):
    try:
        logger.info("获取股票基本面数据1...")
        # 使用httpUtil中的get_daily_market_data方法
        data = get_daily_market_data(
            start_date=end_date,
            end_date=end_date,
            fields="code,name,roe,fzl,dsyl,z50,z197"
        )
        
        if not data:
            logger.warning("基本面数据1获取失败: 无数据返回")
            return pd.DataFrame()
        
        # 将数据转换为DataFrame
        df = pd.DataFrame(data)
        
        # 数据校验和处理
        required_cols = ['code', 'name', 'roe', 'fzl', 'dsyl', 'z50']
        if not all(col in df.columns for col in required_cols):
            raise ValueError("缺少必要数据列")
        
        # 转换数据类型
        for col in ['roe', 'fzl', 'dsyl']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        logger.info(f"成功获取{len(df)}条基本面数据1")
        return df
    except Exception as e:
        logger.error(f"获取股票基本面数据1出错: {e}")
        return pd.DataFrame()

# 获取全市场股票的净利润同比增长率:sjltz,净利润环比增长率:sjlhz,行业板块:z50
def get_stock_fundamental2_symbols(start_date, end_date, symbols):
    try:
        # 使用httpUtil中的get_nb_report_data方法
        data = get_nb_report_data(
            start_date=start_date,
            end_date=end_date,
            fields="code,name,sjltz,sjlhz",
            code=','.join(symbols)
        )
        
        if not data:
            logger.warning("基本面数据2获取失败: 无数据返回")
            return pd.DataFrame()
        
        # 将数据转换为DataFrame
        df = pd.DataFrame(data)
        
        # 数据校验和处理
        required_cols = ['code', 'name', 'sjltz', 'sjlhz']
        if not all(col in df.columns for col in required_cols):
            raise ValueError("缺少必要数据列")
        
        # 转换数据类型
        for col in ['sjltz', 'sjlhz']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        return df
    except Exception as e:
        logger.error(f"获取股票基本面数据2出错: {e}")
        return pd.DataFrame()

# 分批次获取全市场股票的基本面2数据
def get_all_stock_fundamental2(start_date, end_date):
    logger.info("开始分批次获取全市场股票基本面数据2...")
    all_symbols = get_all_stock_symbols()
    all_fundamental2_data = pd.DataFrame()
    
    total_batches = (len(all_symbols) + 49) // 50
    logger.info(f"共需处理{total_batches}个批次，每批50只股票")
    
    for i in range(0, len(all_symbols), 50):
        batch_symbols = all_symbols[i:i+50]
        batch_data = get_stock_fundamental2_symbols(start_date, end_date, batch_symbols)
        if batch_data.empty:
            logger.warning(f"Batch {i//50 + 1} returned empty data.")
        all_fundamental2_data = pd.concat([all_fundamental2_data, batch_data], ignore_index=True)
    
    logger.info(f"成功获取{len(all_fundamental2_data)}条基本面数据2")
    return all_fundamental2_data

# 计算行业平均PE
def calculate_industry_pe(df):
    industry_pe = df.groupby('z50')['dsyl'].mean().reset_index()
    industry_pe.columns = ['z50', 'industry_avg_pe']
    return industry_pe

# 根据策略筛选和排序股票
def filter_and_sort_stocks(end_date):
    logger.info(f"开始执行基本面分析任务，日期: {end_date}")
    start_date = '2025-01-01'
    
    logger.info("获取基本面数据1...")
    fundamental1_df = get_stock_fundamental1_symbols(start_date, end_date)
    logger.info("获取基本面数据2...")
    fundamental2_df = get_all_stock_fundamental2(start_date, end_date)
    
    if fundamental1_df.empty or fundamental2_df.empty:
        logger.error("基本面数据获取失败")
        return pd.DataFrame()
    
    logger.info("合并基本面数据...")
    # 合并基本面数据
    merged_df = pd.merge(fundamental1_df, fundamental2_df, on='code', suffixes=('_1', '_2'))
    
    # 计算PEG
    merged_df['peg'] = merged_df['dsyl'] / merged_df['sjltz']
    
    # 计算行业平均PE
    industry_pe_df = calculate_industry_pe(merged_df)
    merged_df = pd.merge(merged_df, industry_pe_df, on='z50', how='left')
    
    # 计算行业相对PE
    merged_df['relative_pe'] = merged_df['dsyl'] / merged_df['industry_avg_pe']
    
    logger.info("应用筛选条件...")
    # 筛选条件
    filtered_df = merged_df[
        (merged_df['sjltz'] >= 20) &
        (merged_df['sjlhz'] >= 10) &
        (merged_df['peg'] <= 0.9) &
        (merged_df['roe'] >= 10) &
        (merged_df['fzl'] <= 70) &
        (merged_df['relative_pe'] <= 1.15)
    ]
    
    logger.info(f"筛选后剩余{len(filtered_df)}只股票")
    
    # 排序
    sorted_df = filtered_df.sort_values(by=['sjltz', 'sjlhz', 'roe'], ascending=[False, False, False])# type: ignore
    
    logger.info("构建数据库插入数据...")
    gupiao_db = []
    for index, row in sorted_df.iterrows():
        # 创建新字典避免修改原数据（可选）
        new_item = {
            "code": row["code"],
            "name": row["name_1"],
            "star_lev": 0,
            "star_type": "5",
            "industry":row["z197"],
            "concept":row["z50"],
            "insert_time":end_date
        }
        #new_item = {**item, "rank": index,"date":start_date}
        gupiao_db.append(new_item)
    
    logger.info("分配星级...")
    jb_db = assign_stars(gupiao_db)

    logger.info("写入数据库...")
    create_table_and_insert_data(
        data=jb_db,
        table_name='ai_stock_evaluation',
        config=DATABASE_GROUPS
    )
    logger.info("基本面分析任务执行完成")


# 主程序
if __name__ == "__main__":
    filter_and_sort_stocks("2025-07-04")
